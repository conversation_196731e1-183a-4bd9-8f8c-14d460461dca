import logging
from typing import Dict, Any

logger = logging.getLogger('themethodbot.fee_calculator')

def calculate_fees_without_delivery(fees_data: Dict[str, Any], subtotal: float, is_cad: bool = False) -> Dict[str, Any]:
    """Calculate all fee-related values, excluding delivery fee."""
    # Always use the provided subtotal parameter
    actual_subtotal = subtotal

    # NEW PRICING MODEL: Subtract $20 from subtotal and add tax + $9 fee
    # Calculate discounted subtotal (subtract $20)
    discounted_subtotal = max(0, round(actual_subtotal - 20, 2))
    savings = round(actual_subtotal - discounted_subtotal, 2)

    # Set fixed fee of $9
    fixed_fee = 9.00

    # No overflow fee in the new model
    overflow_fee = 0

    # Extract fees from fees_data (for compatibility)
    service_fee = float(fees_data.get('service_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Calculate total fees (tax + $9 fee)
    total_fees = round(taxes + fixed_fee, 2)
    final_fees = total_fees

    # Calculate final total (discounted subtotal + tax + fixed fee)
    final_total = round(discounted_subtotal + taxes + fixed_fee, 2)

    # Return all calculated values
    return {
        'subtotal': actual_subtotal,
        'discounted_subtotal': discounted_subtotal,
        'savings': savings,
        'overflow_fee': overflow_fee,
        'service_fee': service_fee,
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'uber_one_discount': uber_one_discount,
        'total_fees': total_fees,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad,
        'fixed_fee': fixed_fee
    }
