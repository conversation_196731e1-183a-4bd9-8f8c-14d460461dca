import asyncio
import discord
from discord.ext import commands
from discord import app_commands
import requests
import json
import re
import os
from dotenv import load_dotenv
import logging
import time
import uuid
import sys
import aiohttp
import traceback
import urllib.parse
import base64
import ssl
import datetime
from collections import defaultdict
from typing import Dict, Any, Optional, List
import random

# Import local modules
from themethodbot.common.check_group_order import (
    make_api_request,
    check_group_order,
    process_group_order,
    extract_uuid_from_link
)

# Import latestsummary function
from themethodbot.common.latestsummary import latestsummary

# Set up logging
logger = logging.getLogger('themethodbot.bot')

# Load environment variables
load_dotenv()

# Load local environment variables if available
local_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
if os.path.exists(local_env_path):
    load_dotenv(local_env_path)
    logger.info(f"Loaded local environment variables from {local_env_path}")

# Define completion GIFs
COMPLETION_GIFS = [
    "https://media.discordapp.net/attachments/978162420423999498/1360139294890655936/alert_1.gif?ex=67fa07ee&is=67f8b66e&hm=3a983f0f8ae1980bc4abe3fc77efa37b61c8fb862570944ce1ca4290ab3e4b78&=",
    "https://media.discordapp.net/attachments/978162420423999498/1360136757508706424/online-shop.gif?ex=67fa0591&is=67f8b411&hm=35f7807cfda96c95db9f02440bf54459ed8c1fdaf19e071828ff579b9624d57d&="
]

# Environment variables
UBER_COOKIE = os.getenv('UBER_COOKIE')
TOKEN_2 = os.getenv('TOKEN_2')  # For Discord API operations

# Log environment variables (with sensitive info masked)
logger.info(f"UBER_COOKIE set: {bool(UBER_COOKIE)}")
logger.info(f"TOKEN_2 set: {bool(TOKEN_2)}")

# If UBER_COOKIE is not set, try to get it from the global environment
if not UBER_COOKIE:
    try:
        # Try to import from the global common.bot
        from common.bot import UBER_COOKIE as GLOBAL_UBER_COOKIE
        if GLOBAL_UBER_COOKIE:
            UBER_COOKIE = GLOBAL_UBER_COOKIE
            logger.info("Using UBER_COOKIE from global common.bot")
    except ImportError:
        logger.error("Failed to import UBER_COOKIE from global common.bot")

# Add this list for completion gifs
COMPLETION_GIFS = [
    "https://c.tenor.com/De8gDcguE70AAAAd/tenor.gif",
    "https://c.tenor.com/sjwmn6VDG3MAAAAd/tenor.gif",
    "https://c.tenor.com/zDChDUnpKpAAAAAd/tenor.gif",
    "https://c.tenor.com/i1stwMn0fowAAAAd/tenor.gif",
    "https://c.tenor.com/rQny9TWXi0IAAAAd/tenor.gif"
]

# Random names for joining group orders
RANDOM_NAMES = [
    "TheMethod", "MethodUser", "UberEatsMethod", "MethodGuest",
    "MethodCustomer", "MethodClient", "MethodVisitor"
]

async def extract_group_link(message):
    """Extract Uber Eats group order link from message content or embeds."""
    pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

    # Check message content
    match = re.search(pattern, message.content)
    if match:
        return match.group(0)

    # Check embeds
    for embed in message.embeds:
        if embed.description:
            match = re.search(pattern, embed.description)
            if match:
                return match.group(0)

    return None

async def fetch_order_details(order_id: str, session: Optional[aiohttp.ClientSession] = None) -> dict:
    """Fetch order details from UberEats API."""
    url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"

    logger.info(f"🔍 Attempting to fetch details for order: {order_id}")

    timeout = aiohttp.ClientTimeout(total=30, connect=10)
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    headers = {
        "accept": "*/*",
        "content-type": "application/json",
        "x-csrf-token": "x",
        "cookie": UBER_COOKIE if UBER_COOKIE else "",
        "origin": "https://www.ubereats.com",
        "referer": f"https://www.ubereats.com/orders/{order_id}"
    }

    # Log the cookie being used (first 50 chars)
    cookie_preview = UBER_COOKIE[:50] + "..." if UBER_COOKIE else "None"
    logger.info(f"Using cookie (preview): {cookie_preview}")

    payload = {
        "orderUuid": order_id,
        "timezone": "America/New_York"
    }

    # Check if we need to create a new session or use the provided one
    should_close_session = False
    if session is None:
        session = aiohttp.ClientSession(timeout=timeout)
        should_close_session = True

    try:
        async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
            logger.info(f"API response status: {response.status}")

            if response.status == 200:
                data = await response.json()

                # Log the raw response data (truncated)
                response_preview = str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
                logger.info(f"API response preview: {response_preview}")

                # Process the response data
                if data.get('data', {}).get('orders'):
                    order_data = data['data']['orders'][0]

                    # Extract store information
                    store_info = order_data.get('store', {})
                    store_name = store_info.get('title', 'Unknown Store')
                    logger.info(f"Store info: {store_info}")

                    # Extract delivery information
                    delivery_info = order_data.get('delivery', {})
                    logger.info(f"Delivery info: {delivery_info}")
                    delivery_status = delivery_info.get('state', {}).get('type', 'Unknown')
                    delivery_time = delivery_info.get('dropoffTime', 'Unknown')
                    delivery_address = delivery_info.get('location', {}).get('address', {}).get('address1', 'Unknown Address')

                    # Extract order items
                    cart_items = []
                    cart_data = order_data.get('cart', {})
                    logger.info(f"Cart data: {cart_data}")
                    for item in cart_data.get('items', []):
                        item_name = item.get('title', 'Unknown Item')
                        item_quantity = item.get('quantity', 1)
                        cart_items.append(f"{item_name} (x{item_quantity})")

                    # Extract order status
                    order_status_data = order_data.get('orderStatus', {})
                    logger.info(f"Order status data: {order_status_data}")
                    order_status = order_status_data.get('state', {}).get('type', 'Unknown')
                    order_phase = order_status_data.get('phase', 'Unknown')

                    # Create result dictionary
                    result = {
                        'order_id': order_id,
                        'store_name': store_name,
                        'delivery_status': delivery_status,
                        'delivery_time': delivery_time,
                        'delivery_address': delivery_address,
                        'cart_items': cart_items,
                        'order_status': order_status,
                        'order_phase': order_phase,
                        'order_link': f"https://www.ubereats.com/orders/{order_id}"
                    }

                    logger.info(f"✅ Final processed data: {result}")
                    return result
                else:
                    logger.error(f"<:warning:1360153488629432351> No orders found in API response")
                    logger.error(f"Response data: {data}")
                    return None
            else:
                response_text = await response.text()
                logger.error(f"<:warning:1360153488629432351> API request failed with status {response.status}")
                logger.error(f"Response text: {response_text}")
                return None
    except Exception as e:
        logger.error(f"<:warning:1360153488629432351> Error fetching order details: {str(e)}")
        return None
    finally:
        # Close the session if we created it
        if should_close_session and session and not session.closed:
            await session.close()

async def track_order_status(order_id: str, channel, session: Optional[aiohttp.ClientSession] = None):
    """Tracks order status and sends updates via embeds."""
    logger.info(f"🔄 THEMETHODBOT: Starting track_order_status for order_id: {order_id}")
    logger.info(f"Channel: {channel.name if hasattr(channel, 'name') else 'Unknown channel'}")
    logger.info(f"Session: {session}")
    try:
        order_id_match = re.search(r"orders/([a-f0-9-]+)", order_id)
        if order_id_match:
            order_id = order_id_match.group(1)

        url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"
        last_status = None
        tracking = True

        # Use provided session or create a new one
        should_close_session = False
        if session is None:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            session = aiohttp.ClientSession(timeout=timeout)
            should_close_session = True

        # Create SSL context
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        try:
            while tracking:
                try:
                    headers = {
                        "accept": "*/*",
                        "content-type": "application/json",
                        "x-csrf-token": "x",
                        "cookie": UBER_COOKIE if UBER_COOKIE else "",
                        "origin": "https://www.ubereats.com",
                        "referer": f"https://www.ubereats.com/orders/{order_id}"
                    }

                    # Log headers (with sensitive info masked)
                    logger.info(f"API Headers: accept={headers['accept']}, content-type={headers['content-type']}, x-csrf-token={headers['x-csrf-token']}, cookie=***MASKED***, origin={headers['origin']}, referer={headers['referer']}")

                    payload = {
                        "orderUuid": order_id,
                        "timezone": "America/New_York"
                    }

                    # Add retry logic
                    for retry in range(3):
                        try:
                            async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    logger.info(f"API response status: {response.status}")

                                    # Log the raw response data (truncated)
                                    response_preview = str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
                                    logger.info(f"API response preview: {response_preview}")

                                    if not data.get('data', {}).get('orders'):
                                        await channel.send(content="<:warning:1360153488629432351> Order not found")
                                        return

                                    order = data['data']['orders'][0]
                                    analytics = order.get('analytics', {}).get('data', {})
                                    order_status = analytics.get('order_status')
                                    order_phase = order.get('orderInfo', {}).get('orderPhase')

                                    # Extract store name from multiple possible locations
                                    store_name = 'Unknown Store'

                                    # Try to get from store object
                                    store_info = order.get('store', {})
                                    if store_info and store_info.get('title'):
                                        store_name = store_info.get('title')
                                        logger.info(f"Found store name from store.title: {store_name}")

                                    # Try to get from orderSummary
                                    order_summary = order.get('orderSummary', {})
                                    if order_summary and order_summary.get('restaurantName'):
                                        store_name = order_summary.get('restaurantName')
                                        logger.info(f"Found store name from orderSummary.restaurantName: {store_name}")

                                    # Try to get from activeOrderOverview
                                    active_order = order.get('activeOrderOverview', {})
                                    if active_order:
                                        # Check if subtitle contains the restaurant name (not order summary)
                                        subtitle = active_order.get('subtitle', '')
                                        if subtitle and not ('item' in subtitle.lower() or '$' in subtitle):
                                            store_name = subtitle
                                            logger.info(f"Found store name from activeOrderOverview.subtitle: {store_name}")

                                    # Try to get from feedCards
                                    for card in order.get('feedCards', []):
                                        if card.get('type') == 'restaurant':
                                            restaurant_name = card.get('title')
                                            if restaurant_name:
                                                store_name = restaurant_name
                                                logger.info(f"Found store name from feedCards: {store_name}")
                                                break

                                    logger.info(f"Final store name: {store_name}")

                                    # If order_status is None, try to determine it from other fields
                                    if not order_status:
                                        active_order = order.get('activeOrderOverview', {})
                                        eta = active_order.get('title', '')
                                        if 'delivered' in eta.lower():
                                            order_status = 'DELIVERED'
                                        elif 'canceled' in eta.lower() or 'cancelled' in eta.lower():
                                            order_status = 'CANCELED'
                                        else:
                                            order_status = 'Unknown'

                                    # Only send update if status has changed
                                    if order_status != last_status or (order_phase == "COMPLETED" and tracking):
                                        last_status = order_status

                                        # Update tracking status if this is themethodbot
                                        if 'active_tracking' in globals() and order_id in globals()['active_tracking']:
                                            globals()['active_tracking'][order_id]['last_status'] = order_status
                                            # Save tracking data if save_tracking_data function exists
                                            if 'save_tracking_data' in globals():
                                                asyncio.create_task(globals()['save_tracking_data']())

                                        embed = discord.Embed(
                                            title="Order Status Update",
                                            color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                                        )

                                        if order_status == "DELIVERED" or order_phase == "COMPLETED":
                                            embed.title = "✅ Order Delivered!"
                                            embed.color = discord.Color.from_rgb(87, 242, 135)  # Green
                                            tracking = False  # Stop tracking once delivered
                                        elif order_status == "CANCELED":
                                            embed.title = "<:warning:1360153488629432351> Order Canceled"
                                            embed.color = discord.Color.from_rgb(237, 66, 69)  # Red
                                            tracking = False  # Stop tracking if canceled
                                        elif order_status == "EnrouteToRestaurant":
                                            embed.description = "**Driver is heading to the Store** <:car:1360177292730568865>\n\nYour order is being processed and a driver has been assigned."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status == "AtRestaurant":
                                            embed.description = "**Driver has arrived at the Store** <:store:1360153496376315984>\n\nYour driver is waiting for the restaurant to prepare your order."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/RpwAYxrSG5sPWyqfn6ATu2TNjZmN6gkIkHtFFZZjTbM/https/i.gyazo.com/ee7b0b676c0801ccf07414cc4ab8e42a.gif?width=72&height=72")
                                        elif order_status == "OrderPlaced":
                                            embed.description = "**Order has been placed with the Store** <:promotion:1360153519415361546>\n\nThe restaurant has received your order and will begin preparing it soon."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/I7dVXtanMLs7mQcko43gu-a7sk3Xs_uapvPvZubbAo8/https/i.gyazo.com/5ec4b7416f8329666b073f424a420949.png")
                                        elif order_status == "Preparing":
                                            embed.description = "**Store is preparing your order** <:cooking:1360375582017192200>\n\nThe restaurant is now cooking your food. It will be ready for pickup soon."
                                            embed.set_thumbnail(url="https://i.imgur.com/SAuvaKY.gif")
                                        elif order_status == "ReadyForPickup":
                                            embed.description = "**Order is ready for pickup** <:placeholder:1360153497869488137>\n\nYour food is ready and waiting for the driver to pick it up."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status == "PickedUp":
                                            embed.description = "**Driver has picked up your order** <:car:1360177292730568865>\n\nYour driver has your food and will be heading to your location soon."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status in ["EnrouteToDropoff", "EnrouteToEater"]:
                                            embed.description = "**Driver is on the way to you** <:car:1360177292730568865>\n\nYour driver is en route to your delivery location."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                        elif order_status in ["TimeToMeetCourier", "ArrivedAtDropoff"]:
                                            embed.description = "**The driver is about to drop off your order!**\n\n<:bell:1360375940059762708> **Be ready to pick it up** <:bell:1360375940059762708>\n\nYour driver has arrived at your location or will be there momentarily."
                                            embed.color = discord.Color.gold()
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/1pG3uOqsTvi3SZB5bcTG4I32e9bFkiYk9MpfLv45oBg/https/i.gyazo.com/a92427656e0c609ad54bb3c064ec099f.gif?width=72&height=72")
                                        elif order_status == "FindingNewCourier":
                                            embed.description = "**Finding a new driver** <:car:1360177292730568865>\n\nThe previous driver is unavailable. A new driver will be assigned shortly."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/jvNBD8wrP7OzMoJqJaZ8e_V3bDyVvGy3djjZdtBynEc/https/i.gyazo.com/fae7779eb22aaf6a0284d927bfdea74b.gif?width=72&height=72")
                                        else:
                                            embed.description = f"**Current Status:** {order_status}\n\nYour order is being processed. Please wait for updates."

                                        # Set thumbnail for remaining statuses if not already set
                                        if not hasattr(embed, 'thumbnail') or not embed.thumbnail or not embed.thumbnail.url:
                                            if order_status == "DELIVERED" or order_phase == "COMPLETED":
                                                embed.set_thumbnail(url=random.choice(COMPLETION_GIFS))
                                            elif order_status == "CANCELED":
                                                embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139294890655936/alert_1.gif?ex=67fa07ee&is=67f8b66e&hm=3a983f0f8ae1980bc4abe3fc77efa37b61c8fb862570944ce1ca4290ab3e4b78&=")
                                            else:
                                                embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139708234993674/loading.gif?ex=67fa0850&is=67f8b6d0&hm=a4286ff0bd61b871ec51cb03ce8a589cd24d93f101bb10fa414567816d9c4a57&=")

                                        # Add order status field with the correct emoji
                                        status_emoji_display = "🔄"  # Default refresh emoji
                                        if order_status == "DELIVERED" or order_phase == "COMPLETED":
                                            status_emoji_display = "✅"
                                        elif order_status == "CANCELED":
                                            status_emoji_display = "<:warning:1360153488629432351>"

                                        # Add timestamp
                                        embed.timestamp = datetime.datetime.now()

                                        # Send the embed
                                        await channel.send(embed=embed)

                                        # If delivered, send completion message
                                        if order_status == "DELIVERED" or order_phase == "COMPLETED":
                                            await channel.send("<@1348156296704032929> Enjoy your order! could u please vouch for us 😄 <#1348113337195434066>, btw when you reach 10 vouches you get the loyal customer role, check <#1348113029778243675> for more info!")
                                            return

                                        # If canceled, stop tracking
                                        if order_status == "CANCELED":
                                            return

                                    break  # Break retry loop on success
                                else:
                                    logger.error(f"API request failed with status {response.status}, retry {retry+1}/3")
                                    if retry == 2:  # Last retry
                                        await channel.send(content=f"<:warning:1360153488629432351> Failed to fetch order status (HTTP {response.status})")
                                        return
                        except Exception as e:
                            logger.error(f"Error during API request: {e}")
                            if retry == 2:  # Last retry
                                await channel.send(content=f"<:warning:1360153488629432351> Error tracking order: {str(e)}")
                                return
                            await asyncio.sleep(2)  # Wait before retry

                    # Wait before checking again (30 seconds)
                    await asyncio.sleep(30)

                except asyncio.CancelledError:
                    logger.info(f"Tracking for order {order_id} was cancelled")
                    return
                except Exception as e:
                    logger.error(f"Error tracking order: {e}")
                    await asyncio.sleep(60)  # Wait a minute before retrying

        finally:
            # Close the session if we created it
            if should_close_session and session and not session.closed:
                await session.close()

    except Exception as e:
        logger.error(f"Error in track_order_status: {e}")
        await channel.send(content=f"<:warning:1360153488629432351> Error tracking order: {str(e)}")

async def cerv(interaction: discord.Interaction):
    """Sends Cerv's payment details as an embed."""
    embed = discord.Embed(
        title="<:moneybag:1360153494031564840> Cerv's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057669368119360/newtap_to_pay.gif?ex=67fd5f3b&is=67fc0dbb&hm=42192a1552979a7f853a00feaf335d4a27651a0f0e87526948406a64766f2dc3&=&width=800&height=800")  # Payment icon

    # Card/Digital Payments (grouped)
    embed.add_field(
        name="<:credit:1360153483747131442> Card & Digital Payments",
        value="[**Pay with Card/ApplePay/GooglePay/CashApp**](https://buy.stripe.com/28ocNa7GRaO1anScMQ)",
        inline=False
    )

    # Bank Transfers (grouped)
    embed.add_field(
        name="<:bank:1360177291552100495> Bank Transfers",
        value="**Zelle:** `**********`\n**Venmo:** `@CervMethod` *(F&F)*",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="<:credit:1360153483747131442> PayPal",
        value="**Username:** `@ItsCerv` *(F&F)*\n[**PayPal.me Link**](https://paypal.me/ItsCerv)",
        inline=False
    )

    # Add footer with helpful tip
    embed.set_footer(text="Tip: Make sure to use Friends & Family for PayPal/Venmo payments")

    await interaction.response.send_message(embed=embed)

async def nelo(interaction: discord.Interaction):
    """Sends Nelo's payment details as an embed."""
    embed = discord.Embed(
        title="<:moneybag:1360153494031564840> Nelo's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360134428197785702/tap-to-pay.gif?ex=67faac25&is=67f95aa5&hm=5002dbdb209f6f5dff7ee250cc7cbf0f552beb21d08926b82f364ba604221635&=")  # Payment icon

    # Card/Digital Payments (grouped)
    embed.add_field(
        name="<:credit:1360153483747131442> Card & Digital Payments",
        value="[**Pay with Card/ApplePay/GooglePay/CashApp**](https://buy.stripe.com/00g8zi2IH2HbfpSdQQ)",
        inline=False
    )

    # Bank Transfers (grouped)
    embed.add_field(
        name="<:bank:1360177291552100495> Bank Transfers",
        value="**Zelle:** `**********`\n**Venmo:** `@Howard-Chen-75` *(F&F)*",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="<:credit:1360153483747131442> PayPal",
        value="**Username:** `@HowardChen754` *(F&F)*\n[**PayPal.me Link**](https://paypal.me/HowardChen754)",
        inline=False
    )

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

    await interaction.response.send_message(embed=embed)

async def Glitchyz(interaction: discord.Interaction):
    """Sends Glitchyz's payment details as an embed."""
    embed = discord.Embed(
        title="<:moneybag:1360153494031564840> Glitchyz's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360134428197785702/tap-to-pay.gif?ex=67faac25&is=67f95aa5&hm=5002dbdb209f6f5dff7ee250cc7cbf0f552beb21d08926b82f364ba604221635&=")  # Payment icon

    # Bank Transfers (grouped)
    embed.add_field(
        name="<:bank:1360177291552100495> Bank Transfers",
        value="**Zelle:** `**********`\n**Venmo:** `@GlitchUE` *(F&F)*",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="<:credit:1360153483747131442> PayPal",
        value="**Username:** `@GlitchUE` *(F&F)*\n[**PayPal.me Link**](https://paypal.me/GlitchUE)",
        inline=False
    )

    # Add footer with helpful tip
    embed.set_footer(text="Tip: Make sure to use Friends & Family for PayPal/Venmo payments")

    await interaction.response.send_message(embed=embed)

async def track(interaction: discord.Interaction, order_id: str):
    """Track an Uber Eats order status."""
    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Send confirmation to user
        await interaction.followup.send("✅ Started tracking order!", ephemeral=True)

        # Start tracking in background
        asyncio.create_task(track_order_status(order_id, interaction.channel))

    except Exception as e:
        logger.error(f"<:warning:1360153488629432351> Error in track command: {str(e)}")
        await interaction.followup.send(
            "<:warning:1360153488629432351> An error occurred while processing the command.",
            ephemeral=True
        )
