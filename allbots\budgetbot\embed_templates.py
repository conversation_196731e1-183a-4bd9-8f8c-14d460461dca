import discord
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('budgetbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Removed divider

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360123760404861099/image.png?ex=67f9f976&is=67f8a7f6&hm=a7554ba62db0fcf30412967936f2aa2360dd861eb70fb8bc8592c1a12b68c03a&=&format=webp&quality=lossless")  # Lock icon

    # Add footer with helpful tip
    embed.set_footer(text="If you continue to have issues, please contact support")

    return embed

def create_non_promo_embed():
    """Create a modern embed for stores not in promo."""
    embed = discord.Embed(
        title="🔎 Find Eligible Stores",
        description="**The store you're looking for isn't in our promo. Follow these steps to find eligible restaurants:**",
        color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
    )

    # Removed divider

    # Step 1
    embed.add_field(
        name="🔸 Step 1: Open the Promo Link",
        value="Open [**this link**](http://tiny.cc/52qc001) in Chrome or Safari",
        inline=False
    )

    # Step 2
    embed.add_field(
        name="🔹 Step 2: Enter Your Address",
        value="Log in to Uber Eats and enter your delivery address",
        inline=False
    )

    # Step 3
    embed.add_field(
        name="🔸 Step 1: Open the Promo Link in a NEW tab",
        value="Open [**this link**](http://tiny.cc/52qc001) in Chrome or Safari",
        inline=False
    )

    # Set a thumbnail instead of full image for cleaner look
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360124317802827817/image.png?ex=67f9f9fb&is=67f8a87b&hm=f071a1fa92c39ac78c4243aec512b29d2f6968c500de6fcec6109eb449a3b50b&=&format=webp&quality=lossless")  # Search/store icon

    # Add a footer with a tip
    embed.set_footer(text="Tip: If you still don't see eligible stores, keep opening the link in your browser")

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a modern order summary embed with enhanced visuals."""
    embed = discord.Embed(
        title="🍽️ Budget Bites Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')

    # Check if the store is in promo
    is_promo = result.get('is_promo', True)
    promo_status = "✅ Store is in promo!" if is_promo else "❌ Store is not in promo"

    embed.description = f"**🎟️ Promo Status:** {promo_status}\n**🔗 [Group Order Link]({group_link})**\n\n"

    # Removed divider

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Cart items with better formatting
    if cart_items:
        embed.add_field(
            name="🛒 Order Items",
            value="\n".join(cart_items),
            inline=False
        )

    # Price breakdown with better formatting
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"

    # Original vs. discounted price
    embed.add_field(
        name="💰 Price Breakdown",
        value=f"**Original Subtotal:** `{currency}{fee_calculations.get('subtotal', 0):.2f}`\n" +
              f"**Discounted (70% OFF):** `{currency}{fee_calculations.get('discounted_subtotal', 0):.2f}`\n" +
              f"**You Save:** `{currency}{fee_calculations.get('savings', 0):.2f}`",
        inline=False
    )

    # Fees breakdown
    fees_str = ""

    if fee_calculations.get('overflow_fee', 0) > 0:
        fees_str += f"**Overflow Fee:** `{currency}{fee_calculations.get('overflow_fee', 0):.2f}`\n"

    # Calculate total fees to check if they're zero
    service_fee = fee_calculations.get('service_fee', 0)
    ca_driver_benefit = fee_calculations.get('ca_driver_benefit', 0)
    taxes = fee_calculations.get('taxes', 0)

    # Check if service fee and taxes are both $0, which indicates "You pay for your portion" setting
    # This is more reliable than checking total_fees which can include overflow fees
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.add_field(
            name="💸 Fees & Taxes",
            value="**Unable to fetch pricing**\nGroup order is set to \"You pay for your portion\"",
            inline=False
        )
    else:
        fees_str += f"**Service Fee:** `{currency}{service_fee:.2f}`\n"

        if ca_driver_benefit > 0:
            fees_str += f"**Driver Benefit:** `{currency}{ca_driver_benefit:.2f}`\n"

        fees_str += f"**Taxes:** `{currency}{taxes:.2f}`"

        embed.add_field(
            name="💸 Fees & Taxes",
            value=fees_str,
            inline=False
        )

    # Final total with emphasis - only show if it's not zero and we have valid fee data
    final_total = fee_calculations.get('final_total', 0)
    if final_total > 0 and not (service_fee == 0 and taxes == 0 and ca_driver_benefit == 0):
        embed.add_field(
            name="💲 Final Total",
            value=f"**`{currency}{final_total:.2f}`**",
            inline=False
        )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360124857496371290/image.png?ex=67f9fa7b&is=67f8a8fb&hm=26e3e488ce4b79b3e6178ae8484bf9c93c5b007d9dc08ae1ad052a7382c7313c&=&format=webp&quality=lossless")  # Food/order icon

    # Add footer with helpful information - only show estimate warning if we have valid pricing
    if service_fee == 0 and taxes == 0 and ca_driver_benefit == 0:
        embed.set_footer(text="Budget Bites | Order Summary")
    else:
        embed.set_footer(text="⚠️ This is an ESTIMATED price. Please wait for a Chef to confirm your final total. Thank you!")

    return embed

def check_order_limits(subtotal: float, is_cad: bool) -> Optional[discord.Embed]:
    """Check if order meets minimum/maximum requirements with modern embed."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 24.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least `{currency}{min_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Removed divider

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please add more items to your cart to meet the minimum requirement.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Required",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Minimum Required:** `{currency}{min_order:.2f}`\n" +
                  f"**Amount to Add:** `{currency}{(min_order - subtotal):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360124981924593694/image.png?ex=67f9fa99&is=67f8a919&hm=39321bd800579e7cc54d24f45451777ad77dc70ece19430c9406682b2c16b2cd&=&format=webp&quality=lossless")  # Warning icon

        return embed

    elif subtotal > max_order:
        embed = discord.Embed(
            title="⚠️ Maximum Order Limit",
            description=f"Your subtotal must be at most `{currency}{max_order:.2f}` before fees and taxes.",
            color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
        )

        # Removed divider

        # Add action required
        embed.add_field(
            name="👉 Action Required",
            value="Please remove some items from your cart to meet the maximum limit.",
            inline=False
        )

        # Add current vs required
        embed.add_field(
            name="📊 Current vs Limit",
            value=f"**Current Subtotal:** `{currency}{subtotal:.2f}`\n" +
                  f"**Maximum Allowed:** `{currency}{max_order:.2f}`\n" +
                  f"**Amount to Remove:** `{currency}{(subtotal - max_order):.2f}`",
            inline=False
        )

        # Set a nice thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360124981924593694/image.png?ex=67f9fa99&is=67f8a919&hm=39321bd800579e7cc54d24f45451777ad77dc70ece19430c9406682b2c16b2cd&=&format=webp&quality=lossless")  # Warning icon

        return embed

    return None

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Removed divider

    # Add troubleshooting steps
    embed.add_field(
        name="🔍 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360124981924593694/image.png?ex=67f9fa99&is=67f8a919&hm=39321bd800579e7cc54d24f45451777ad77dc70ece19430c9406682b2c16b2cd&=&format=webp&quality=lossless")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360125178490916887/image.png?ex=67f9fac8&is=67f8a948&hm=9d44635567e796022e8ab92d93d89568c61c6e3ae3bc5744d96513ac84021009&=&format=webp&quality=lossless")  # Loading animation

    return embed

def create_payment_embed() -> discord.Embed:
    """Create a modern payment methods embed for Shams."""
    embed = discord.Embed(
        title="💰 Sham’s Payments",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Removed divider

    # Card/Digital Payments (grouped)
    embed.add_field(
        name="💳 Card & Digital Payments",
        value="[**Pay with Card/Apple Pay/Google Pay**](https://buy.stripe.com/14k02t418gEwbpS6oo)",
        inline=False
    )

    # Crypto
    embed.add_field(
        name="🪙 Cryptocurrency",
        value="**BTC:** `**********************************`\n**LTC:** `MH1KG3LoJ35w4YQ1gHMTceNXg1Rdv1e6H1`\n**ETH:** `******************************************`\n**SOL:** `DsqasZShX5sg4kX18teS7S2ayrhDWhXUfa941v3agjMy`",
        inline=False
    )

    # Set a modern thumbnail with Shams' image
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/1349142062691520605/1356835835420348547/IMG_7285.png?ex=67f9e0d7&is=67f88f57&hm=b27241cdda4ae899cb56b2e5e88df7ff64da8a5324ca3b70ebb68829517267a7&=&format=webp&quality=lossless&width=394&height=385")  # Shams' image

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

    return embed
