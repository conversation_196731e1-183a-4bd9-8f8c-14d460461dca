import asyncio
import aiohttp
import logging
import json
import re
import os
import random
import base64
import urllib.parse
from typing import Dict, Any, Optional

# Configure logger
logger = logging.getLogger('themethodbot.check_group_order')

# Load environment variables
PROMO_25_COOKIE = os.getenv('25_PROMO')
UBER_COOKIE = os.getenv('UBER_COOKIE')

# Random names for joining group orders
RANDOM_NAMES = [
    "TheMethod", "MethodUser", "UberEatsMethod", "MethodGuest",
    "MethodCustomer", "MethodClient", "MethodVisitor"
]

# Default headers for API requests
DEFAULT_HEADERS = {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "cookie": PROMO_25_COOKIE if PROMO_25_COOKIE else "",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "x-csrf-token": "x",
    "origin": "https://www.ubereats.com",
    "referer": "https://www.ubereats.com/",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin"
}

async def make_api_request(session: aiohttp.ClientSession, endpoint: str, payload: dict) -> dict:
    """Make an API request to Uber Eats."""
    try:
        url = f"https://www.ubereats.com/_p/api/{endpoint}"
        timeout = aiohttp.ClientTimeout(total=10)

        logger.debug(f"Making API request to: {url}")
        logger.debug(f"Payload: {payload}")

        async with session.post(
            url,
            json=payload,
            headers=DEFAULT_HEADERS,
            timeout=timeout
        ) as response:
            response_data = await response.json()
            logger.debug(f"API Response: {json.dumps(response_data, indent=2)}")
            return {
                "status": response.status,
                "data": response_data
            }
    except Exception as e:
        logger.error(f"API request failed: {str(e)}")
        logger.exception("Full exception:")
        return {
            "status": 500,
            "error": str(e)
        }

async def validate_session(session: aiohttp.ClientSession) -> bool:
    """Validate the session by making a test request."""
    try:
        async with session.get("https://www.ubereats.com/", headers=DEFAULT_HEADERS) as response:
            return response.status == 200
    except Exception as e:
        logger.error(f"Session validation failed: {str(e)}")
        return False

async def get_order_details_from_data(session: aiohttp.ClientSession, group_uuid: str) -> Dict[str, Any]:
    """Extract order details from the group order data."""
    try:
        # Get cart details
        cart_payload = {
            "draftOrderUuid": group_uuid
        }

        cart_result = await make_api_request(session, "getDraftOrderV1", cart_payload)

        if cart_result.get('status') != 200:
            logger.error(f"Failed to get cart details: {cart_result}")
            return None

        cart_data = cart_result.get('data', {}).get('data', {})

        if not cart_data:
            logger.error("No cart data received")
            return None

        # Extract store information
        store_uuid = cart_data.get('storeUuid')
        if not store_uuid:
            logger.error("No store UUID found in cart data")
            return None

        # Extract cart items
        cart_items = []
        for item in cart_data.get('items', []):
            item_name = item.get('title', 'Unknown Item')
            item_quantity = item.get('quantity', 1)
            item_price = item.get('price', {}).get('subtotal', {}).get('amount', 0) / 100.0

            cart_items.append({
                'name': item_name,
                'quantity': item_quantity,
                'price': item_price
            })

        # Extract location information
        address_components = cart_data.get('deliveryLocation', {}).get('address', {}).get('address', {})

        address = address_components.get('streetAddress', '')
        city = address_components.get('city', '')
        state = address_components.get('state', '')
        zipcode = address_components.get('postalCode', '')

        # Extract fees
        fees_data = {}
        cart_totals = cart_data.get('totals', {})

        if cart_totals:
            # Convert cents to dollars
            fees_data['subtotal'] = cart_totals.get('subtotal', 0) / 100.0
            fees_data['tax'] = cart_totals.get('tax', 0) / 100.0
            fees_data['total'] = cart_totals.get('total', 0) / 100.0
            fees_data['delivery_fee'] = cart_totals.get('deliveryFee', 0) / 100.0
            fees_data['service_fee'] = cart_totals.get('serviceFee', 0) / 100.0
            fees_data['small_order_fee'] = cart_totals.get('smallOrderFee', 0) / 100.0
            fees_data['tip'] = cart_totals.get('tip', 0) / 100.0

            # Additional fees that might be present
            fees_data['ca_driver_benefit'] = cart_totals.get('caDriverBenefitsFee', 0) / 100.0
            fees_data['taxes'] = cart_totals.get('tax', 0) / 100.0
            fees_data['uber_one_discount'] = cart_totals.get('uberOneDeliveryFeeDiscount', 0) / 100.0

        return {
            'store_uuid': store_uuid,
            'cart_items': cart_items,
            'address': address,
            'city': city,
            'state': state,
            'zipcode': zipcode,
            'fees_data': fees_data
        }

    except Exception as e:
        logger.error(f"Error getting order details: {str(e)}")
        return None

async def get_store_url_from_data(session: aiohttp.ClientSession, data: Dict[str, Any]) -> Optional[str]:
    """Generate a store URL from the order data."""
    try:
        store_uuid = data.get('store_uuid')
        if not store_uuid:
            logger.error("No store UUID found in data")
            return None

        # Get store details
        store_payload = {
            "storeUuid": store_uuid
        }

        store_result = await make_api_request(session, "getStoreV1", store_payload)

        if store_result.get('status') != 200:
            logger.error(f"Failed to get store details: {store_result}")
            return None

        # Extract store data from nested structure
        store_data = store_result.get('data', {}).get('data', {})
        store_slug = store_data.get('slug')

        if not store_slug:
            logger.error(f"Failed to get store slug. Store data: {store_data}")
            return None

        # Create location payload for URL
        location_payload = {
            "address": {
                "address": {
                    "streetAddress": data.get('address', ''),
                    "city": data.get('city', ''),
                    "state": data.get('state', ''),
                    "postalCode": data.get('zipcode', '')
                }
            }
        }

        # Properly encode the location payload
        json_str = json.dumps(location_payload)
        base64_encoded = base64.b64encode(json_str.encode()).decode()
        encoded_pl = urllib.parse.quote(base64_encoded)

        # Construct the final URL
        return (
            f"https://www.ubereats.com/store/{store_slug}/{store_uuid}"
            f"?diningMode=DELIVERY&pl={encoded_pl}&ps=1"
        )

    except Exception as e:
        logger.error(f"Error getting store URL: {str(e)}")
        return None

async def check_store_promo(store_url: str, cookie: str = None) -> bool:
    """Check if a store is eligible for the promo."""
    try:
        headers = DEFAULT_HEADERS.copy()
        if cookie:
            headers["cookie"] = cookie

        async with aiohttp.ClientSession() as session:
            async with session.get(store_url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    html = await response.text()

                    # Check for promo UUIDs in the HTML
                    promo_uuid_1 = "2c1e668b-8d83-42a4-99db-985c72fa74de"  # US promo
                    promo_uuid_2 = "807f13d5-0e00-4df2-9894-6b23e73e9fa6"  # Canada promo

                    # Additional UUIDs to check
                    additional_uuids = [
                        "ae2e9f79-6189-4f46-bf20-5b41debb570a",  # Old US promo UUID
                        "449baf92-dda2-44d1-9ad6-24033c26f516",
                        "c8e35ed9-3e53-4c12-a63a-7eb5a683a64f",
                        "4a6e5b6a-6784-4d83-8b90-9e9e4b3b7119",
                        "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"
                    ]

                    # Check for primary UUIDs in different formats
                    is_promo_1 = promo_uuid_1 in html or f'"uuid":"{promo_uuid_1}"' in html
                    is_promo_2 = promo_uuid_2 in html or f'"uuid":"{promo_uuid_2}"' in html

                    # Check for additional UUIDs
                    additional_matches = []
                    for i, uuid in enumerate(additional_uuids):
                        is_match = uuid in html
                        additional_matches.append(is_match)
                        logger.info(f"Additional UUID {i+1} check: {uuid} - Found: {is_match}")

                    # Also check for any UUID pattern
                    uuid_pattern = r'"uuid":"([a-f0-9-]+)"'
                    import re
                    found_uuids = re.findall(uuid_pattern, html)
                    logger.info(f"All UUIDs found in response: {found_uuids}")

                    # Store is in promo if any UUID is found
                    is_promo = is_promo_1 or is_promo_2 or any(additional_matches)

                    # Debug logging
                    logger.info(f"Promo UUID 1 check: {promo_uuid_1} - Found: {is_promo_1}")
                    logger.info(f"Promo UUID 2 check: {promo_uuid_2} - Found: {is_promo_2}")

                    # Save a sample of the content for debugging
                    with open('themethodbot_promo_response_sample.txt', 'w') as f:
                        f.write(html[:10000])  # Save first 10K characters
                    logger.info("Saved response sample to themethodbot_promo_response_sample.txt")

                    logger.info(f"Store promo check result: {is_promo}")
                    return is_promo

        return False
    except Exception as e:
        logger.error(f"Error checking store promo: {str(e)}")
        return False

def extract_uuid_from_link(link: str) -> str:
    """Extract UUID from Uber Eats group order link."""
    pattern = r"(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)([a-zA-Z0-9-]+)(?:/join)?"
    match = re.search(pattern, link)
    if not match:
        raise ValueError("Invalid Uber Eats group order link format")
    return match.group(1)

async def check_group_order(url: str) -> bool:
    """Check if the group order is valid and accessible."""
    try:
        # Extract UUID from the URL
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', url)
        if not uuid_match:
            logger.error("Failed to extract UUID from group order URL")
            return False

        group_uuid = uuid_match.group(1)

        async with aiohttp.ClientSession(cookies={'25_PROMO': os.getenv('25_PROMO')}) as session:
            if not await validate_session(session):
                logger.error("Invalid session")
                return False

            # Get cart details to verify the group order is valid
            order_details = await get_order_details_from_data(session, group_uuid)
            if not order_details:
                logger.error("Failed to get order details")
                return False

            return True
    except Exception as e:
        logger.error(f"Error checking group order: {str(e)}")
        return False

async def process_group_order(group_link: str) -> dict:
    """Process a group order link and extract all relevant information."""
    try:
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', group_link)
        if not uuid_match:
            return None

        group_uuid = uuid_match.group(1)
        current_cookie = os.getenv('25_PROMO')
        session = None

        try:
            session = aiohttp.ClientSession(cookies={'25_PROMO': current_cookie})
            # Initial join and data fetch with regular cookie
            join_payload = {
                "draftOrderUuid": group_uuid,
                "nickname": random.choice(RANDOM_NAMES)
            }

            join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload)

            if (join_result.get('data', {}).get('data', {}).get('message') == 'cart.not_editable.group_order_locked' or
                join_result.get('data', {}).get('message') == 'cart.not_editable.group_order_locked'):
                return {
                    'error': {
                        'type': 'LOCKED_ORDER',
                        'message': 'cart.not_editable.group_order_locked'
                    }
                }

            data = join_result.get('data', {}).get('data', {})
            if not data:
                logger.error("No data received from join request")
                return None

            # Get order details
            order_details = await get_order_details_from_data(session, group_uuid)
            if not order_details:
                logger.error("Failed to get order details")
                return None

            # Get store URL for promo check
            store_url = await get_store_url_from_data(session, order_details)
            is_promo = False

            if store_url:
                logger.info(f"Generated store URL: {store_url}")

                # First try with regular cookie
                try:
                    is_promo = await check_store_promo(store_url)
                    logger.info(f"Regular cookie promo check result: {is_promo}")
                except Exception as e:
                    logger.error(f"Error checking promo with regular cookie: {str(e)}")

                # If no promo found, try with Canada cookie
                if not is_promo and os.getenv('CANADA_COOKIE'):
                    logger.info("No promo found with regular cookie, trying Canada cookie...")
                    try:
                        current_cookie = os.getenv('CANADA_COOKIE')
                        is_promo = await check_store_promo(store_url, current_cookie)
                        logger.info(f"Canada cookie promo check result: {is_promo}")
                    except Exception as e:
                        logger.error(f"Error checking promo with Canada cookie: {str(e)}")

            # Extract fees data
            fees_data = order_details.get('fees_data', {})

            # Return the processed data
            return {
                'group_link': group_link,
                'cart_items': order_details.get('cart_items', []),
                'location': {
                    'address': order_details.get('address'),
                    'city': order_details.get('city'),
                    'state': order_details.get('state'),
                    'zipcode': order_details.get('zipcode')
                },
                'is_promo': is_promo,
                'store_url': store_url,
                'fees': fees_data
            }

        finally:
            if session:
                await session.close()

    except Exception as e:
        logger.error(f"Error processing group order: {str(e)}")
        return None

def format_cart_items(cart_items):
    """Format cart items for display in embeds."""
    formatted_items = []
    for item in cart_items:
        name = item.get('name', 'Unknown Item')
        quantity = item.get('quantity', 1)
        price = item.get('price', 0.0)

        if quantity > 1:
            formatted_items.append(f"{name} (x{quantity}) - ${price:.2f}")
        else:
            formatted_items.append(f"{name} - ${price:.2f}")

    return formatted_items
