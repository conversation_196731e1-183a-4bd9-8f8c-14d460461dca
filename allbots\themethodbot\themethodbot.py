import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import time
import aiohttp
import ssl
import json
import datetime
from typing import Dict, Optional, Any, List
import psutil

import sys
import os

# Import from our local common folder
from themethodbot.common.bot import (
    cerv,
    nelo,
    Glitchyz,
    track,
    extract_group_link
)

# Import our payment app module
from themethodbot.paymentapp import PaymentMethodButtons, PaymentMethodSelector, setup_payment_views

# Custom cooldown implementation
check_command_cooldowns = {}
COOLDOWN_TIME = 5.0  # 5 seconds cooldown

from common.bot import process_cart_items, calculate_fees, check_order_limits, create_order_summary_embed, create_locked_order_embed, create_non_promo_embed
from common.check_group_order import process_group_order

from themethodbot.common.latestsummary import latestsummary

# Import from the main common folder for functions not yet migrated
from common.bot import (
    fetch_order_details,  # Use the global fetch_order_details function
    track_order_status,   # Use the global track_order_status function
    open_store,
    close_store,
    vouchtop,
    selectedstores,
    process_group_order,
)

# Import our custom fee calculator that excludes delivery fee
from themethodbot.fee_calculator import calculate_fees_without_delivery

# Import our custom embed templates
from themethodbot.embed_templates import (
    create_locked_order_embed,
    create_order_summary_embed,
    check_order_limits,
    create_error_embed,
    create_processing_embed,
    create_non_promo_embed,
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('themethodbot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('themethodbot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("DISCORD_GUILD_ID"))
TOKEN_2 = os.getenv("TOKEN_2")  # Add TOKEN_2 for channel operations
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot with optimized settings
bot = commands.Bot(
    command_prefix="!",
    intents=intents,
    dm_permission=True,
    case_insensitive=True,  # Make commands case-insensitive
    max_messages=10000,     # Increase message cache for better performance
    heartbeat_timeout=150.0 # Increase heartbeat timeout for stability
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Performance metrics
command_metrics: Dict[str, Dict[str, float]] = {}

# Cache for frequently accessed data
data_cache: Dict[str, Any] = {}

# Path to the tracking data file
TRACKING_DATA_FILE = 'tracking_data.json'

# Path to the staff clock-in data file
CLOCK_DATA_FILE = 'clock_data.json'

# Active tracking information
active_tracking: Dict[str, Dict[str, Any]] = {}

# Staff clock-in tracking
clocked_in_staff = set()

# Queue system constants
TICKET_CATEGORY_ID = 1340194718637625395  # Category ID for tickets
QUEUE_CATEGORY_ID = 1361051057475682537  # Category ID for queue

# Status message ID to update
STATUS_MESSAGE_ID = 1365395438542262345

# Function to save clock data
async def save_clock_data():
    """Save clocked-in staff data to file."""
    try:
        with open(CLOCK_DATA_FILE, 'w') as f:
            json.dump(list(clocked_in_staff), f)
        logger.info(f"Saved clock data: {clocked_in_staff}")
    except Exception as e:
        logger.error(f"Error saving clock data: {e}")

# Function to load clock data
async def load_clock_data():
    """Load clocked-in staff data from file."""
    global clocked_in_staff
    try:
        if os.path.exists(CLOCK_DATA_FILE):
            with open(CLOCK_DATA_FILE, 'r') as f:
                data = json.load(f)
                clocked_in_staff = set(data)
            logger.info(f"Loaded clock data: {clocked_in_staff}")
    except Exception as e:
        logger.error(f"Error loading clock data: {e}")

# Function to update the status message with clocked-in staff
async def update_status_message(guild):
    """Update the status message with the current clocked-in staff."""
    try:
        # Get the status channel
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Get the status message
        try:
            status_message = await status_channel.fetch_message(STATUS_MESSAGE_ID)
        except discord.NotFound:
            logger.error(f"Status message with ID {STATUS_MESSAGE_ID} not found")
            return False
        except Exception as e:
            logger.error(f"Error fetching status message: {e}")
            return False

        # Check if the store is open
        store_open = await is_store_open(guild)

        # Get the list of clocked-in staff members
        staff_mentions = []
        for staff_id in clocked_in_staff:
            member = guild.get_member(staff_id)
            if member:
                staff_mentions.append(member.mention)

        # Create the appropriate embed based on store status
        if store_open:
            embed = discord.Embed(
                title="🚀 The Method is Now Open!",
                description="**We are now accepting orders!** Place your order using the instructions below.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Add information about how to order
            embed.add_field(
                name="📋 How to Order",
                value=f"Check out <#{1340210714891128882}> for detailed instructions on how to place an order.",
                inline=False
            )

            # Add information about payment methods
            embed.add_field(
                name="💳 Payment Methods",
                value="We accept various payment methods including PayPal, Venmo, and Cash App.",
                inline=False
            )

            # Add clocked-in staff field
            if staff_mentions:
                embed.add_field(
                    name="👨‍🍳 Chefs Clocked In Doing Orders:",
                    value=", ".join(staff_mentions),
                    inline=False
                )

            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614541195218975/a.jpg?ex=67c18d60&is=67c03be0&hm=31b0756c3bb9d314fecab34039c85025b4271a349a8d5f9a7267a4c74e9864bc&=&format=webp&width=947&height=541")
            embed.set_footer(text="The Method | Fast & Reliable Service")
            embed.timestamp = datetime.datetime.now()

        else:
            embed = discord.Embed(
                title="🔴 The Method is Now Closed!",
                description="**We are currently closed.** Please check back later for updates.",
                color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614540758749245/ClosedBanner.jpg?ex=67fa3de0&is=67f8ec60&hm=b74d7be3b974fc13fa60f06fa6f241dc9f02816a50ac51dff4809dbfa5da0ecb&=&format=webp&width=1466&height=838")
            embed.set_footer(text="The Method | Currently Closed")
            embed.timestamp = datetime.datetime.now()

        # Update the message
        await status_message.edit(embed=embed)
        logger.info(f"Updated status message with {len(staff_mentions)} clocked-in staff members")
        return True

    except Exception as e:
        logger.error(f"Error updating status message: {e}")
        logger.error(traceback.format_exc())
        return False

# Function to move a channel to the queue category
async def move_to_queue(channel, position=None):
    """Move a channel to the queue category.

    Args:
        channel: The channel to move
        position: Optional position in the category (default: None, which appends to the end)
    """
    try:
        # Get the queue category
        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            logger.error(f"Queue category with ID {QUEUE_CATEGORY_ID} not found")
            return False

        # Move the channel to the queue category
        await channel.edit(category=queue_category, position=position)

        # Get the number of channels in the queue for position display
        queue_position = len(queue_category.channels)

        # Create a queue position embed
        embed = discord.Embed(
            title="🔄 Added to Queue",
            description=f"This channel has been moved to the queue.",
            color=discord.Color.blue()
        )

        # Add queue position field
        embed.add_field(
            name="📊 Queue Position",
            value=f"#{queue_position}",
            inline=True
        )

        # Add timestamp
        embed.timestamp = datetime.datetime.now()

        # Send the embed to the channel
        await channel.send(embed=embed)

        logger.info(f"Moved channel {channel.name} to queue at position {queue_position}")
        return True
    except Exception as e:
        logger.error(f"Error moving channel to queue: {e}")
        logger.error(traceback.format_exc())
        return False

class OrderTrackButton(discord.ui.View):
    def __init__(self, order_link: str):
        super().__init__()

        # Create a blue (primary) style button
        track_button = discord.ui.Button(
            label="🔎 Track Order",
            style=discord.ButtonStyle.primary,  # This makes it blue
            url=order_link
        )
        self.add_item(track_button)

async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

async def get_latest_embed(channel) -> Optional[discord.Embed]:
    """Get the latest embed from a channel."""
    try:
        async for message in channel.history(limit=10):
            if message.embeds:
                return message.embeds[0]
        return None
    except Exception as e:
        logger.error(f"Error getting latest embed: {e}")
        return None

async def is_store_open(guild) -> bool:
    """Check if the store is open by examining the latest embed in the status channel."""
    try:
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Check channel name first (most reliable indicator)
        if "open" in status_channel.name.lower():
            return True

        # Check latest embed as fallback
        latest_embed = await get_latest_embed(status_channel)
        if latest_embed and latest_embed.title:
            # If the title contains "Open", the store is open
            return "Open" in latest_embed.title or "🟢" in latest_embed.title
        return False
    except Exception as e:
        logger.error(f"Error checking if store is open: {e}")
        return False

@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Initialize the HTTP session
    await get_session()

    # Record start time for uptime tracking
    bot.launch_time = time.time()

    # Initialize tracking tasks list
    bot.tracking_tasks = []

    # Load saved tracking data and resume tracking
    await load_tracking_data()

    # Load clock data
    await load_clock_data()

    # Set up payment views
    setup_payment_views(bot, command_metrics)
    logger.info("<:check:1360153501866393692> Payment views registered")

    # Initialize background tasks list
    bot.bg_tasks = []
    logger.info("<:check:1360153501866393692> Initialized background tasks")

async def save_tracking_data():
    """Save active tracking information to a file."""
    try:
        # Create a copy of the tracking data with only serializable information
        tracking_data = {}
        for order_id, data in active_tracking.items():
            tracking_data[order_id] = {
                'channel_id': data['channel_id'],
                'start_time': data['start_time'],
                'last_status': data.get('last_status'),
                'order_link': data.get('order_link')
            }

        # Save to file
        with open(TRACKING_DATA_FILE, 'w') as f:
            json.dump(tracking_data, f)

        logger.info(f"Saved tracking data for {len(tracking_data)} orders")
    except Exception as e:
        logger.error(f"Error saving tracking data: {e}")
        logger.error(traceback.format_exc())

async def load_tracking_data():
    """Load tracking data from file and resume tracking."""
    global active_tracking

    try:
        # Check if the file exists
        if not os.path.exists(TRACKING_DATA_FILE):
            logger.info("No tracking data file found")
            return

        # Load data from file
        with open(TRACKING_DATA_FILE, 'r') as f:
            tracking_data = json.load(f)

        if not tracking_data:
            logger.info("No tracking data to resume")
            return

        logger.info(f"Found tracking data for {len(tracking_data)} orders")

        # Resume tracking for each order
        for order_id, data in tracking_data.items():
            try:
                # Get the channel
                channel_id = data['channel_id']
                channel = bot.get_channel(int(channel_id))

                if not channel:
                    logger.warning(f"Channel {channel_id} not found for order {order_id}")
                    continue

                # Store in active tracking
                active_tracking[order_id] = {
                    'channel_id': channel_id,
                    'start_time': data['start_time'],
                    'last_status': data.get('last_status'),
                    'order_link': data.get('order_link')
                }

                # Get a session
                session = await get_session()

                # Resume tracking
                order_link = data.get('order_link', f"https://www.ubereats.com/orders/{order_id}")
                tracking_task = asyncio.create_task(
                    track_order_status(order_id, channel, session, bot_name='themethodbot')
                )

                # Store the task
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)

                # Notify in the channel
                await channel.send(f"<:check:1360153501866393692> Resumed tracking for order {order_id}")

                logger.info(f"Resumed tracking for order {order_id} in channel {channel_id}")
            except Exception as e:
                logger.error(f"Error resuming tracking for order {order_id}: {e}")
                logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"Error loading tracking data: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle app command errors."""
    # Handle errors
    logger.error(f"Command error: {error}")
    traceback.print_exception(type(error), error, error.__traceback__)

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"<:check:1360153501866393692> Logged in as {bot.user}")
    logger.info(f"Command mode: {'Global' if COMMANDS_GLOBAL else 'Guild-only'}")

    # Memory usage info
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    logger.info(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")

    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"<:check:1360153501866393692> Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"<:check:1360153501866393692> Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Failed to sync commands: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_message(message):
    # Process commands first
    await bot.process_commands(message)

    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    # Check if the message is in a channel with 'ticket' in the name and in the ticket category
    channel = message.channel
    if (isinstance(channel, discord.TextChannel) and
            'ticket' in channel.name.lower() and
            channel.category_id == TICKET_CATEGORY_ID):
        # Check if this is a successful order message (contains a group order link)
        group_link = await extract_group_link(message)
        if group_link:
            # Process the order first (let the normal flow continue)
            pass
        # Check if this is a message from a staff member with a specific command
        elif message.content.lower() == '!addtoqueue' and message.author.guild_permissions.manage_channels:
            # Move the channel to the queue
            await move_to_queue(channel)
            return

    try:
        # Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        # Normalize the link to ensure consistent matching
        # Remove any trailing parameters or fragments
        if '?' in group_link:
            group_link = group_link.split('?')[0]
        if '#' in group_link:
            group_link = group_link.split('#')[0]

        # Ensure the link ends with /join for consistency
        if not group_link.endswith('/join'):
            if group_link.endswith('/'):
                group_link = group_link + 'join'
            else:
                group_link = group_link + '/join'

        logger.info(f"<:search:1360154069028835410> Group order link detected: {group_link}")

        # Send a processing message with a nice embed
        processing_message = await message.channel.send(embed=create_processing_embed())

        # Process group order
        result = await process_group_order(group_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_link

        # Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await processing_message.delete()
                await message.channel.send(embed=create_locked_order_embed())
                return

        if not result:
            await processing_message.delete()
            await message.channel.send(embed=create_locked_order_embed())
            return

        # Skip promo check and proceed directly to order summary

        # Process cart items
        cart_items = []
        calculated_subtotal = 0
        is_cad = False

        if 'cart_items' in result:
            for item in result['cart_items']:
                price = item.get('price', 0)
                quantity = item.get('quantity', 1)
                title = item.get('title', 'Unknown Item')
                cart_items.append(f"{title} x{quantity} (${price/100:.2f})")
                calculated_subtotal += (price * quantity) / 100

        # Calculate fees (excluding delivery fee)
        if 'fees' in result and result['fees']:
            fees_data = result['fees']
            # Check if taxes is a string and contains 'CA', otherwise assume not CAD
            taxes_value = fees_data.get('taxes', '')
            is_cad = isinstance(taxes_value, str) and 'CA' in taxes_value

            # Use the subtotal from fees_data if available, otherwise use calculated_subtotal
            if 'subtotal' in fees_data and fees_data['subtotal'] > 0:
                subtotal = float(fees_data['subtotal'])
                logger.info(f"Using subtotal from fees_data: ${subtotal:.2f}")
            else:
                subtotal = calculated_subtotal
                logger.info(f"Using calculated subtotal: ${subtotal:.2f}")

            fee_calculations = calculate_fees_without_delivery(fees_data, subtotal, is_cad)
        else:
            # Fallback if no fees data is available
            subtotal = calculated_subtotal
            logger.info(f"No fees data available, using calculated subtotal: ${subtotal:.2f}")
            fee_calculations = calculate_fees_without_delivery({}, subtotal, False)

        # Delete the processing message
        await processing_message.delete()

        # Create and send order summary
        summary_embed = create_order_summary_embed(result, cart_items, fee_calculations)
        await message.channel.send(embed=summary_embed)

        # Check and send order limits warning if needed
        limit_warning = check_order_limits(subtotal, is_cad)
        if limit_warning:
            await message.channel.send(embed=limit_warning)
        else:
            # Send a nicely formatted success message
            success_embed = discord.Embed(
                title="<:check:1360153501866393692> Order Processed Successfully",
                description="Your order has been processed and is ready for review.\n\n**Next Steps**\nPlease wait patiently. A Chef or Waiter will assist you shortly.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )
            # Add a thumbnail for a modern look
            success_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Food/order icon
            success_embed.set_footer(text="Thank you for using The Method!")
            await message.channel.send(embed=success_embed)

    except Exception as e:
        logger.error(f"Error processing group order link: {str(e)}")
        logger.error(traceback.format_exc())

        # Try to delete the processing message if it exists
        try:
            if 'processing_message' in locals():
                await processing_message.delete()
        except:
            pass

        # Send a nicely formatted error message
        await message.channel.send(embed=create_error_embed(str(e)))

@bot.tree.command(
    name="selectedstores",
    description="Get promo link for a specific address and save store list",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    address="The delivery address to check"
)

async def selectedstores_command(interaction: discord.Interaction, address: str):
    """Gets a promo link for the specified address and saves store list."""
    await track_command_metrics("selectedstores")(selectedstores)(interaction, address)

@bot.tree.command(
    name="vouchtop",
    description="Display vouch leaderboard",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def vouchtop_command(interaction: discord.Interaction):
    """Display the vouch leaderboard."""
    await track_command_metrics("vouchtop")(vouchtop)(interaction)

@bot.tree.command(
    name="track",
    description="Track an Uber order status",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    order_id="The Uber order ID to track"
)

async def track_command(interaction: discord.Interaction, order_id: str):
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Send confirmation to user
        await interaction.followup.send("<:check:1360153501866393692> Started tracking order!", ephemeral=True)

        # Get a session
        session = await get_session()

        # Store tracking information
        active_tracking[order_id] = {
            'channel_id': interaction.channel.id,
            'start_time': time.time(),
            'last_status': None,
            'order_link': f"https://www.ubereats.com/orders/{order_id}"
        }

        # Save tracking data to file
        await save_tracking_data()

        # Start tracking in background
        tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session))

        # Store the task for cleanup
        if not hasattr(bot, 'tracking_tasks'):
            bot.tracking_tasks = []
        bot.tracking_tasks.append(tracking_task)

        logger.info(f"Started tracking task for order {order_id}")

        # Track metrics
        execution_time = time.time() - start_time
        if "track" not in command_metrics:
            command_metrics["track"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["track"]['count'] += 1
        command_metrics["track"]['total_time'] += execution_time
        command_metrics["track"]['max_time'] = max(
            command_metrics["track"]['max_time'],
            execution_time
        )

        logger.debug(f"Command track executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in track command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(name="open",
    description="Opens the store."
)
@app_commands.checks.has_any_role(1340830489719734443)  # Only allow the new role

async def open_store_slash(interaction: discord.Interaction):
    """Slash command to open the store."""
    await track_command_metrics("open_store")(open_store)(interaction)

@bot.tree.command(
    name="close",
    description="Closes the store."
)
@app_commands.guilds(GUILD)  # Fixed
@app_commands.checks.has_any_role(1340830489719734443)  # Staff role check

async def close_store_slash(interaction: discord.Interaction):
    """Slash command to close the store."""
    await track_command_metrics("close_store")(close_store)(interaction)

@bot.tree.command(
    name="latestsummary",
    description="Calculate order summary with manual subtotal and fees",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    subtotal="Enter the original total amount (e.g., 21.28)",
    fees="Enter the fees and tax amount (e.g., 5.99)",
    grouporderlink="(Optional) Group order link to fetch location and items"
)

async def latestsummary_command(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    """Wrapper for latestsummary function in bot.py"""
    await track_command_metrics("latestsummary")(latestsummary)(interaction, subtotal, fees, grouporderlink)

@bot.tree.command(
    name="cerv",
    description="Cerv's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def cerv_command(interaction: discord.Interaction):
    await track_command_metrics("cerv")(cerv)(interaction)


@bot.tree.command(
    name="nelo",
    description="Nelo's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def nelo_command(interaction: discord.Interaction):
    await track_command_metrics("nelo")(nelo)(interaction)

@bot.tree.command(
    name="glitchyz",
    description="Glitchyz's payment details.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def glitchyz_command(interaction: discord.Interaction):
    await track_command_metrics("glitchyz")(Glitchyz)(interaction)

@bot.tree.command(
    name="check",
    description="Check a group order link and see the summary (only visible to you).",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    group_order_link="The Uber Eats group order link to check"
)
# Custom cooldown implementation
async def check_command(interaction: discord.Interaction, group_order_link: str):
    try:
        # Check if user is on cooldown
        user_id = str(interaction.user.id)
        current_time = time.time()

        if user_id in check_command_cooldowns:
            time_elapsed = current_time - check_command_cooldowns[user_id]
            if time_elapsed < COOLDOWN_TIME:
                time_remaining = round(COOLDOWN_TIME - time_elapsed, 1)
                await interaction.response.send_message(
                    f"⏱️ This command is on cooldown. Please wait {time_remaining} seconds before trying again.",
                    ephemeral=True
                )
                return

        # Update cooldown timestamp
        check_command_cooldowns[user_id] = current_time

        # Defer the response as ephemeral (only visible to the user)
        await interaction.response.defer(ephemeral=True)

        # Process the group order
        result = await process_group_order(group_order_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_order_link

        # Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await interaction.followup.send(embed=create_locked_order_embed())
                return

        if not result:
            await interaction.followup.send(embed=create_locked_order_embed())
            return

        # Skip promo check and proceed directly to order summary

        # Process cart items
        cart_items, calculated_subtotal = process_cart_items(result.get('cart_items', []))

        # Calculate fees
        if 'fees' in result and result['fees']:
            fees_data = result['fees']
            # Check if taxes is a string and contains 'CA', otherwise assume not CAD
            taxes_value = fees_data.get('taxes', '')
            is_cad = isinstance(taxes_value, str) and 'CA' in taxes_value

            # Use the subtotal from fees_data if available, otherwise use calculated_subtotal
            if 'subtotal' in fees_data and fees_data['subtotal'] > 0:
                subtotal = float(fees_data['subtotal'])
                logger.info(f"Using subtotal from fees_data: ${subtotal:.2f}")
            else:
                subtotal = calculated_subtotal
                logger.info(f"Using calculated subtotal: ${subtotal:.2f}")

            # Use our new fee calculator instead of the standard one
            fee_calculations = calculate_fees_without_delivery(fees_data, subtotal, is_cad)
        else:
            # Fallback if no fees data is available
            subtotal = calculated_subtotal
            logger.info(f"No fees data available, using calculated subtotal: ${subtotal:.2f}")
            fee_calculations = calculate_fees_without_delivery({}, subtotal, False)

        # Create and send order summary
        summary_embed = create_order_summary_embed(result, cart_items, fee_calculations)
        await interaction.followup.send(embed=summary_embed)

        # Check and send order limits warning if needed
        limit_warning = check_order_limits(subtotal, is_cad)
        if limit_warning:
            await interaction.followup.send(embed=limit_warning, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in check command: {str(e)}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(f"❌ Error checking group order: {str(e)}")

@bot.tree.command(
    name="sync",
    description="Sync slash commands (admin only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(administrator=True)
async def sync_command(interaction: discord.Interaction):
    """Manually sync slash commands."""
    try:
        await interaction.response.defer(ephemeral=True)

        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            await interaction.followup.send(f"✅ Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            await interaction.followup.send(f"✅ Synced {len(synced)} command(s) to guild")

    except Exception as e:
        logger.error(f"Error syncing commands: {e}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(f"❌ Error syncing commands: {e}")

@bot.tree.command(
    name="pricingmodel",
    description="Shows our new pricing model: Subtotal - $20 + $9 fee",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)

async def pricingmodel(interaction: discord.Interaction):
    """Shows information about our new pricing model."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if we have this in cache
        cache_key = "pricingmodel_embed"
        if cache_key in data_cache:
            logger.debug("Using cached pricingmodel embed")
            embed = data_cache[cache_key]
        else:
            # Create the embed with new pricing model info
            embed = discord.Embed(
                title="<:search:1360154069028835410> Our New Pricing Model",
                description="**We've updated our pricing model! Now you get:**\n\n• Subtotal - $20 discount\n• Tax + $9 fee\n• No promo restrictions - works with any restaurant!",
                color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
            )

            # Add a thumbnail for a modern look
            embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")  # Search/store icon

            # Add spacing to the description
            embed.description += "\n\n"

            # Step 1
            embed.add_field(
                name="🔸 Step 1: Choose Any Restaurant",
                value="With our new pricing model, you can order from **any restaurant** on Uber Eats!",
                inline=False
            )

            # Step 2
            embed.add_field(
                name="🔹 Step 2: Create a Group Order",
                value="Create a group order and share the link in the orders channel",
                inline=False
            )

            # Step 3
            embed.add_field(
                name="🔸 Step 3: Get Your Discount",
                value="We'll automatically apply our discount: Subtotal - $20 + tax + $9 fee",
                inline=False
            )

            # Step 4
            embed.add_field(
                name="🔹 Step 4: Enjoy Your Savings",
                value="Pay the discounted price and enjoy your food!",
                inline=False
            )

            # Add a footer with the new pricing model
            embed.set_footer(text="The Method | New Pricing Model: Subtotal - $20 + tax + $9 fee")

            # Add the screenshot as a smaller image at the bottom
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?width=600&height=338")

            # Cache the embed for future use
            data_cache[cache_key] = embed
            logger.debug("Cached pricingmodel embed")

        await interaction.response.send_message(embed=embed)

        # Track metrics
        execution_time = time.time() - start_time
        if "pricingmodel" not in command_metrics:
            command_metrics["pricingmodel"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["pricingmodel"]['count'] += 1
        command_metrics["pricingmodel"]['total_time'] += execution_time
        command_metrics["pricingmodel"]['max_time'] = max(
            command_metrics["pricingmodel"]['max_time'],
            execution_time
        )

        logger.debug(f"Command pricingmodel executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"<:cancel:1360154555295207596> Error in pricingmodel command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="clockin",
    description="Clock in for your shift",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    user="The user to clock in (staff only)"
)
async def clockin_command(interaction: discord.Interaction, user: discord.Member = None):
    """Clock in for your shift or clock in another user."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if user is authorized
        authorized_users = [572991971359195138, 542140310524788736, 965501524304343082]
        if interaction.user.id not in authorized_users:
            await interaction.response.send_message("⛔ You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Determine who to clock in
        target_user = user if user else interaction.user

        # Add user to clocked-in staff
        global clocked_in_staff
        clocked_in_staff.add(target_user.id)

        # Save clock data
        await save_clock_data()

        # Log the action
        if user:
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) forced clock in for {target_user} ({target_user.id})")
        else:
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) clocked in")

        # Update the status message with the specific message ID
        await update_status_message(interaction.guild)

        # Check if store is closed
        store_open = await is_store_open(interaction.guild)

        if not store_open:
            # Run the open command
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) clocked in and is opening the store")

            # Get the status channel and other required channels/roles
            guild = interaction.guild
            status_channel = guild.get_channel(1340406893927075900)  # Status Channel
            store_channel = guild.get_channel(1340210714891128882)  # Store channel
            customer_role = guild.get_role(1343166726170345523)  # Customer role
            verified_channel = guild.get_channel(1340194718637625397)  # Verified Channel
            orders_channel = guild.get_channel(1340392172691918848)  # Orders Channel

            if not all([status_channel, store_channel, customer_role, verified_channel, orders_channel]):
                await interaction.followup.send("❌ Error: Required channels or roles not found.", ephemeral=True)
                return

            # Change channel name to open status
            await status_channel.edit(name="🟢┃status-open")

            # Update store channel permissions for the customer role
            overwrite = store_channel.overwrites_for(customer_role)
            overwrite.view_channel = True
            await store_channel.set_permissions(customer_role, overwrite=overwrite)

            # Update verified channel permissions (show when open)
            verified_overwrite = verified_channel.overwrites_for(customer_role)
            verified_overwrite.view_channel = True
            await verified_channel.set_permissions(customer_role, overwrite=verified_overwrite)

            # Update orders channel permissions (can type when open)
            orders_overwrite = orders_channel.overwrites_for(customer_role)
            orders_overwrite.send_messages = True
            orders_overwrite.view_channel = True
            await orders_channel.set_permissions(customer_role, overwrite=orders_overwrite)

            # Create view with helpful buttons for a new message
            from common.bot import OrderHelpButtons
            view = OrderHelpButtons()

            # Send a new announcement message with buttons
            announcement_embed = discord.Embed(
                title="🚀 The Method is Now Open!",
                description="**We are now accepting orders!** Place your order using the instructions below.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )

            # Add information about how to order
            announcement_embed.add_field(
                name="📋 How to Order",
                value=f"Check out <#{1340210714891128882}> for detailed instructions on how to place an order.",
                inline=False
            )

            # Send the announcement embed to status channel with buttons
            await status_channel.send(embed=announcement_embed, view=view)

            # Send announcement about who clocked in
            if user:
                await status_channel.send(f"**{target_user.mention} has been clocked in by {interaction.user.mention} and opened the store!**")
                await interaction.followup.send(f"✅ You have successfully clocked in {target_user.display_name} and opened the store!", ephemeral=True)
            else:
                await status_channel.send(f"**{interaction.user.mention} has clocked in and opened the store!**")
                await interaction.followup.send("✅ You have successfully clocked in and opened the store!", ephemeral=True)
        else:
            # Get the status channel
            status_channel = interaction.guild.get_channel(1340406893927075900)  # Status Channel
            if status_channel:
                if user:
                    await status_channel.send(f"**{target_user.mention} has been clocked in by {interaction.user.mention}!**")
                    await interaction.followup.send(f"✅ You have successfully clocked in {target_user.display_name}! Current staff on duty: {len(clocked_in_staff)}", ephemeral=True)
                else:
                    await status_channel.send(f"**{interaction.user.mention} has clocked in!**")
                    await interaction.followup.send(f"✅ You have successfully clocked in! Current staff on duty: {len(clocked_in_staff)}", ephemeral=True)

        # Send confirmation to user
        await interaction.followup.send(f"✅ You have successfully clocked in! Current staff on duty: {len(clocked_in_staff)}", ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "clockin" not in command_metrics:
            command_metrics["clockin"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["clockin"]['count'] += 1
        command_metrics["clockin"]['total_time'] += execution_time
        command_metrics["clockin"]['max_time'] = max(
            command_metrics["clockin"]['max_time'],
            execution_time
        )

        logger.debug(f"Command clockin executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in clockin command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="clockout",
    description="Clock out from your shift",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    user="The user to clock out (staff only)"
)
async def clockout_command(interaction: discord.Interaction, user: discord.Member = None):
    """Clock out from your shift."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if user is authorized
        authorized_users = [572991971359195138, 542140310524788736, 965501524304343082]
        if interaction.user.id not in authorized_users:
            await interaction.response.send_message("⛔ You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Determine who to clock out
        target_user = user if user else interaction.user

        # Remove user from clocked-in staff
        global clocked_in_staff
        if target_user.id in clocked_in_staff:
            clocked_in_staff.remove(target_user.id)

        # Log the action
        if user:
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) forced clock out for {target_user} ({target_user.id})")
        else:
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) clocked out")

        # Save clock data
        await save_clock_data()

        # Update the status message with the specific message ID
        await update_status_message(interaction.guild)

        # Check if store is open and if everyone has clocked out
        store_open = await is_store_open(interaction.guild)

        if store_open and len(clocked_in_staff) == 0:
            # Run the close command
            logger.info(f"🕒 {interaction.user} ({interaction.user.id}) clocked out and is closing the store")

            # Get the status channel and other required channels/roles
            guild = interaction.guild
            status_channel = guild.get_channel(1340406893927075900)  # Status Channel
            store_channel = guild.get_channel(1340210714891128882)  # Store Channel
            customer_role = guild.get_role(1343166726170345523)  # Customer role
            verified_channel = guild.get_channel(1340194718637625397)  # Verified Channel
            orders_channel = guild.get_channel(1340392172691918848)  # Orders Channel

            if not all([status_channel, store_channel, customer_role, verified_channel, orders_channel]):
                await interaction.followup.send("❌ Error: Required channels or roles not found.", ephemeral=True)
                return

            # Change status channel name to indicate closed status
            await status_channel.edit(name="🔴┃status")

            # Update store channel permissions for the customer role
            overwrite = store_channel.overwrites_for(customer_role)
            overwrite.view_channel = False
            await store_channel.set_permissions(customer_role, overwrite=overwrite)

            # Update verified channel permissions (hide when closed)
            verified_overwrite = verified_channel.overwrites_for(customer_role)
            verified_overwrite.view_channel = False
            await verified_channel.set_permissions(customer_role, overwrite=verified_overwrite)

            # Update orders channel permissions (can't type when closed)
            orders_overwrite = orders_channel.overwrites_for(customer_role)
            orders_overwrite.send_messages = False
            orders_overwrite.view_channel = True  # Keep visible but can't type
            await orders_channel.set_permissions(customer_role, overwrite=orders_overwrite)

            # Create a new announcement embed
            announcement_embed = discord.Embed(
                title="🔴 The Method is Now Closed!",
                description="**We are currently closed.** Please check back later for updates.",
                color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
            )

            # Send the announcement embed to status channel
            await status_channel.send(embed=announcement_embed)

            # Send announcement about who clocked out
            if user:
                await status_channel.send(f"**{target_user.mention} has been clocked out by {interaction.user.mention} and closed the store!**")
                await interaction.followup.send(f"✅ You have successfully clocked out {target_user.display_name} and closed the store!", ephemeral=True)
            else:
                await status_channel.send(f"**{interaction.user.mention} has clocked out and closed the store!**")

            # Send confirmation to user in the original interaction
            await interaction.followup.send("✅ Service has been closed successfully!", ephemeral=True)
        else:
            # Update the status message with remaining staff
            await update_status_message(interaction.guild)

            # Get the status channel
            status_channel = interaction.guild.get_channel(1340406893927075900)  # Status Channel
            if status_channel:
                if user:
                    await status_channel.send(f"**{target_user.mention} has been clocked out by {interaction.user.mention}!**")
                    await interaction.followup.send(f"✅ You have successfully clocked out {target_user.display_name}! Current staff on duty: {len(clocked_in_staff)}", ephemeral=True)
                else:
                    await status_channel.send(f"**{interaction.user.mention} has clocked out!**")

        # Send confirmation to user
        staff_count_msg = f"Current staff on duty: {len(clocked_in_staff)}" if clocked_in_staff else "No staff currently on duty."
        await interaction.followup.send(f"✅ You have successfully clocked out! {staff_count_msg}", ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "clockout" not in command_metrics:
            command_metrics["clockout"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["clockout"]['count'] += 1
        command_metrics["clockout"]['total_time'] += execution_time
        command_metrics["clockout"]['max_time'] = max(
            command_metrics["clockout"]['max_time'],
            execution_time
        )

        logger.debug(f"Command clockout executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in clockout command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="checklink",
    description="Check a group order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    group_order_link="The group order link to check"
)
@app_commands.checks.cooldown(1, 5.0, key=lambda i: i.user.id)
async def checklink_command(interaction: discord.Interaction, group_order_link: str):
    """Check a group order link and show the summary."""
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)

        # Process the group order link
        from themethodbot.common.check_group_order import process_group_order
        result = await process_group_order(group_order_link)

        if not result:
            await interaction.followup.send("❌ Failed to process the group order link. Please check the link and try again.", ephemeral=True)
            return

        if "error" in result:
            error_type = result.get("error", {}).get("type", "UNKNOWN_ERROR")
            if error_type == "LOCKED_ORDER":
                # Create a locked order embed
                embed = discord.Embed(
                    title="🔒 Group Order is Locked",
                    description="This group order has been locked by the organizer and cannot be modified.",
                    color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
                )
                embed.set_footer(text="The Method | Group Order Checker")
                embed.timestamp = datetime.datetime.now()

                await interaction.followup.send(embed=embed, ephemeral=True)
                return
            else:
                await interaction.followup.send(f"❌ Error: {result.get('error')}", ephemeral=True)
                return

        # Check if the store is in promo
        is_promo = result.get('is_promo', False)

        if not is_promo:
            # Create a store not in promo embed
            embed = discord.Embed(
                title="⚠️ Store Not in Promo",
                description="The selected store is not eligible for our promo.",
                color=discord.Color.from_rgb(254, 231, 92)  # Bright Yellow
            )

            # Add store information
            store_name = result.get('store_name', 'Unknown Store')
            embed.add_field(
                name="🏪 Restaurant",
                value=f"**{store_name}**",
                inline=False
            )

            # Add instructions
            embed.add_field(
                name="🔍 What to do",
                value="Please select a different restaurant that is eligible for our promo. Use the `/storenotinpromo` command for help finding eligible stores.",
                inline=False
            )

            embed.set_footer(text="The Method | Group Order Checker")
            embed.timestamp = datetime.datetime.now()

            await interaction.followup.send(embed=embed, ephemeral=True)
            return

        # Create the order summary embed
        embed = discord.Embed(
            title="🍽️ The Method Order Summary",
            description=f"Here's a summary of your group order:",
            color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
        )

        # Add group order link
        embed.add_field(
            name="🔗 Group Order Link",
            value=f"[Click to open]({group_order_link})",
            inline=False
        )

        # Add restaurant
        store_name = result.get('store_name', 'Unknown Store')
        embed.add_field(
            name="🏪 Restaurant",
            value=f"**{store_name}**",
            inline=False
        )

        # Format cart items
        cart_items = result.get('cart_items', [])
        if cart_items:
            items_text = ""
            for item in cart_items:
                name = item.get('name', 'Unknown Item')
                quantity = item.get('quantity', 1)
                price = item.get('price', 0.0)

                if quantity > 1:
                    items_text += f"╰・ *{name} (x{quantity}) - ${price:.2f}*\n"
                else:
                    items_text += f"╰・ *{name} - ${price:.2f}*\n"

            embed.add_field(
                name="🛒 Order Items",
                value=items_text if items_text else "No items found",
                inline=False
            )

        # Add price breakdown
        fees = result.get('fees', {})
        subtotal = fees.get('subtotal', 0.0)
        discount = subtotal * 0.7  # 70% discount
        discounted_subtotal = subtotal - discount

        # Create price breakdown field
        price_breakdown = f"Subtotal: ${subtotal:.2f}\n"
        price_breakdown += f"Discount (70%): -${discount:.2f}\n"
        price_breakdown += f"Discounted Subtotal: ${discounted_subtotal:.2f}"

        # Create fees field
        delivery_fee = fees.get('delivery_fee', 0.0)
        service_fee = fees.get('service_fee', 0.0)
        taxes = fees.get('tax', 0.0)
        uber_one_discount = fees.get('uber_one_discount', 0.0)

        fees_text = f"Delivery Fee: ${delivery_fee:.2f}\n"
        if uber_one_discount != 0:
            fees_text += f"Uber One Discount: -${abs(uber_one_discount):.2f}\n"
        fees_text += f"Service Fee: ${service_fee:.2f}\n"
        fees_text += f"Taxes: ${taxes:.2f}"

        # Calculate final total
        final_total = discounted_subtotal + delivery_fee + service_fee + taxes + uber_one_discount

        # Add fields in a side-by-side layout
        embed.add_field(
            name="💰 Price Breakdown",
            value=price_breakdown,
            inline=True
        )

        embed.add_field(
            name="📊 Fees & Taxes",
            value=fees_text,
            inline=True
        )

        # Add final total
        embed.add_field(
            name="💵 Final Total",
            value=f"${subtotal:.2f} + fees ➡️ **${final_total:.2f}**",
            inline=False
        )

        # Add warning about estimate
        embed.add_field(
            name="⚠️ Important Note",
            value="This is an ESTIMATE. Please wait for a Chef to confirm your final prices.",
            inline=False
        )

        # Set footer and timestamp
        embed.set_footer(text="The Method | Group Order Checker")
        embed.timestamp = datetime.datetime.now()

        # Send the embed
        await interaction.followup.send(embed=embed, ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "checklink" not in command_metrics:
            command_metrics["checklink"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["checklink"]['count'] += 1
        command_metrics["checklink"]['total_time'] += execution_time
        command_metrics["checklink"]['max_time'] = max(
            command_metrics["checklink"]['max_time'],
            execution_time
        )

        logger.debug(f"Command checklink executed in {execution_time:.4f}s")
    except app_commands.CommandOnCooldown as e:
        await interaction.followup.send(f"⏳ This command is on cooldown. Please try again in {e.retry_after:.1f} seconds.", ephemeral=True)
    except Exception as e:
        logger.error(f"Error in checklink command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="resetclock",
    description="Reset the clock data (staff only)",
    guild=None if COMMANDS_GLOBAL else GUILD
)
async def resetclock_command(interaction: discord.Interaction):
    """Reset the clock data."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if user is authorized
        authorized_users = [572991971359195138, 542140310524788736, 965501524304343082]
        if interaction.user.id not in authorized_users:
            await interaction.response.send_message("⛔ You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)

        # Reset clocked-in staff
        global clocked_in_staff
        clocked_in_staff.clear()

        # Save clock data
        await save_clock_data()

        # Update the status message
        await update_status_message(interaction.guild)

        # Log the action
        logger.info(f"🕒 {interaction.user} ({interaction.user.id}) reset the clock data")

        # Send confirmation to user
        await interaction.followup.send("✅ Clock data has been reset successfully!", ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "resetclock" not in command_metrics:
            command_metrics["resetclock"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["resetclock"]['count'] += 1
        command_metrics["resetclock"]['total_time'] += execution_time
        command_metrics["resetclock"]['max_time'] = max(
            command_metrics["resetclock"]['max_time'],
            execution_time
        )

        logger.debug(f"Command resetclock executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in resetclock command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="testapi",
    description="Test the Uber Eats API connection",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
@app_commands.describe(
    orderlink="Optional order link to test (if not provided, will only test cookie)"
)
async def testapi_slash(interaction: discord.Interaction, orderlink: str = None):
    """Test the Uber Eats API connection."""
    await interaction.response.defer(ephemeral=True)

    try:
        # Get the HTTP session
        session = await get_session()

        # Log the UBER_COOKIE value (first 50 chars)
        from themethodbot.common.bot import UBER_COOKIE
        cookie_preview = UBER_COOKIE[:50] + "..." if UBER_COOKIE else "None"
        logger.info(f"UBER_COOKIE preview: {cookie_preview}")

        # Create a test embed
        embed = discord.Embed(
            title="API Test Results",
            color=discord.Color.blue()
        )

        embed.add_field(
            name="Cookie Status",
            value=f"{'✅ Cookie found' if UBER_COOKIE else '❌ Cookie not found'}\n(Preview: `{cookie_preview}`)",
            inline=False
        )

        # If an order link was provided, test fetching the order
        if orderlink:
            await interaction.followup.send("Testing API with provided order link...", ephemeral=True)

            # Extract order ID
            order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
            if order_id:
                order_id = order_id.group(1)

                # Fetch order details
                from themethodbot.common.bot import fetch_order_details
                order_details = await fetch_order_details(order_id, session)

                if order_details:
                    embed.add_field(
                        name="API Test Result",
                        value="✅ Successfully fetched order details!",
                        inline=False
                    )

                    # Add some order details
                    embed.add_field(
                        name="Store Name",
                        value=f"`{order_details['store_name']}`",
                        inline=True
                    )

                    embed.add_field(
                        name="Delivery Status",
                        value=f"`{order_details['delivery_status']}`",
                        inline=True
                    )

                    embed.add_field(
                        name="Order Items",
                        value="\n".join([f"╰・ *{item}*" for item in order_details['cart_items'][:3]]) +
                              ("\n╰・ *...and more*" if len(order_details['cart_items']) > 3 else ""),
                        inline=False
                    )
                else:
                    embed.add_field(
                        name="API Test Result",
                        value="❌ Failed to fetch order details. Check logs for more information.",
                        inline=False
                    )
            else:
                embed.add_field(
                    name="API Test Result",
                    value="❌ Invalid order link format.",
                    inline=False
                )

        # Send the embed
        await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in testapi command: {e}")
        logger.error(traceback.format_exc())
        await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="ordersuccess",
    description="Track a successful order using order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    orderlink="The order link to track"
)

async def ordersuccess_slash(
    interaction: discord.Interaction,
    orderlink: str
):
    """Slash command to track a successful order."""
    # Track command execution time
    start_time = time.time()

    try:
        logger.info(f"📌 `/ordersuccess` was triggered by {interaction.user} with order link: {orderlink}")
        await interaction.response.defer()

        # Handle channel renaming
        current_channel = interaction.channel
        if current_channel.name.startswith(("nelo4317-", "itscerv-", "_glitchyz-", "ticket-")):
            try:
                ticket_number = re.sub(r"^(nelo4317-|itscerv-|_glitchyz-|ticket-)", "", current_channel.name)
                new_name = f"{ticket_number}-delivering"
                await current_channel.edit(name=new_name)
                logger.info(f"<:check:1360153501866393692> Successfully renamed channel to {new_name}")

                # Move the channel to the queue
                await move_to_queue(current_channel)
            except Exception as e:
                logger.error(f"<:cancel:1360154555295207596> Failed to rename channel: {str(e)}")
                logger.error(traceback.format_exc())

        # Extract order ID and fetch details
        order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if order_id:
            order_id = order_id.group(1)

            # Get the HTTP session
            session = await get_session()

            # Fetch order details with the shared session using the global function
            order_details = await fetch_order_details(order_id, session)

            if order_details:
                # Create the success embed with enhanced modern design
                order_embed = discord.Embed(
                    title="<:startup:1360177289664401418> Order Tracking Initiated",
                    description="Your order has been successfully placed and is now being tracked!",
                    color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                )

                # Set a nice thumbnail - using the original tracking thumbnail
                order_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360136757508706424/online-shop.gif?ex=67fa0591&is=67f8b411&hm=35f7807cfda96c95db9f02440bf54459ed8c1fdaf19e071828ff579b9624d57d&=")

                # Add spacing to the description
                order_embed.description += "\n\n"

                # Restaurant section with emoji (only if not unknown)
                store_name = order_details.get('store', 'Unknown Store')
                if store_name != 'Unknown Store' and store_name != 'N/A' and not ('item' in store_name.lower() or '$' in store_name):
                    order_embed.add_field(
                        name="<:promotion:1360153519415361546> Restaurant",
                        value=f"**{store_name}**",
                        inline=False
                    )

                # ETA section with emoji
                order_embed.add_field(
                    name="<:clock:1360156495517323264> Estimated Arrival",
                    value=f"**{order_details['eta']}**",
                    inline=True
                )

                # Customer section with emoji
                customer_name = order_details.get('customer', 'Unknown')
                if customer_name == 'N/A' or not customer_name:
                    customer_name = interaction.user.display_name

                order_embed.add_field(
                    name="<:personalinformation:1360153515296559285> Customer",
                    value=f"**{customer_name}**",
                    inline=True
                )

                # Add spacing between sections
                # No divider needed

                # Order items section with better formatting
                items_text = order_details['items']
                formatted_items = "\n".join([f"╰・ *{item.strip()}*" for item in items_text.split('•') if item.strip()])
                order_embed.add_field(
                    name="<:shoppingcart:1360153495155642459> Order Items",
                    value=formatted_items if formatted_items else "No items found",
                    inline=False
                )

                # Add spacing between sections
                # No divider needed

                # Delivery address section with code block formatting
                order_embed.add_field(
                    name="<:placeholder:1360153497869488137> Delivery Address",
                    value=f"```{order_details['address']}```",
                    inline=False
                )

                # Order link with button-like formatting
                order_embed.add_field(
                    name="<:link:1360154729002565662> Order Link",
                    value=f"[**Click to view order**]({orderlink})",
                    inline=False
                )

                # Add footer with tracking info and timestamp
                order_embed.set_footer(text="Order is being tracked automatically | The Method")
                order_embed.timestamp = datetime.datetime.now()

                # Create view with tracking button
                view = OrderTrackButton(orderlink)

                # Send the embed with the view and start tracking
                await interaction.followup.send(embed=order_embed, view=view)

                # Store tracking information
                active_tracking[order_id] = {
                    'channel_id': interaction.channel.id,
                    'start_time': time.time(),
                    'last_status': None,
                    'order_link': orderlink
                }

                # Save tracking data to file
                await save_tracking_data()

                # Start tracking in a background task and store it for cleanup
                logger.info(f"THEMETHODBOT: Creating tracking task for order_id: {order_id}")
                logger.info(f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'Unknown channel'}")
                logger.info(f"Session: {session}")

                try:
                    # Use the global track_order_status function
                    tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session, bot_name='themethodbot'))
                    if not hasattr(bot, 'tracking_tasks'):
                        bot.tracking_tasks = []
                    bot.tracking_tasks.append(tracking_task)
                    logger.info(f"Successfully created and stored tracking task for order_id: {order_id}")

                    # Send a message to confirm tracking has started
                    await interaction.followup.send("✅ Order tracking has started! You'll receive status updates in this channel.")
                except Exception as e:
                    logger.error(f"Error creating tracking task: {e}")
                    logger.error(traceback.format_exc())
                    await interaction.followup.send(f"⚠️ Error starting order tracking: {str(e)}")
            else:
                await interaction.followup.send("<:cancel:1360154555295207596> Failed to fetch order details.", ephemeral=True)
        else:
            await interaction.followup.send("<:cancel:1360154555295207596> Invalid order link format.", ephemeral=True)
            return

        logger.info("<:check:1360153501866393692> Order tracking initiated.")

        # Track metrics
        execution_time = time.time() - start_time
        if "ordersuccess" not in command_metrics:
            command_metrics["ordersuccess"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["ordersuccess"]['count'] += 1
        command_metrics["ordersuccess"]['total_time'] += execution_time
        command_metrics["ordersuccess"]['max_time'] = max(
            command_metrics["ordersuccess"]['max_time'],
            execution_time
        )

        logger.debug(f"Command ordersuccess executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in ordersuccess command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        except Exception:
            pass



@bot.tree.command(
    name="cleardelivered",
    description="Close all channels with 'delivered' in the name in the specified category",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def cleardelivered_command(interaction: discord.Interaction):
    """Close all channels with 'delivered' in the name in the specified category."""
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/cleardelivered` command triggered by {interaction.user}")

        # Get the guild and category
        guild = interaction.guild
        category_id = 1354242418211422419
        category = guild.get_channel(category_id)

        if not category:
            await interaction.followup.send(f"<:cancel:1360154555295207596> Category with ID {category_id} not found.", ephemeral=True)
            return

        # Find all channels in the category with "delivered" in the name
        delivered_channels = [channel for channel in category.channels
                             if isinstance(channel, discord.TextChannel) and "delivered" in channel.name.lower()]

        if not delivered_channels:
            await interaction.followup.send("<:check:1360153501866393692> No channels with 'delivered' in the name found in the category.", ephemeral=True)
            return

        # Send initial status message
        await interaction.followup.send(f"🔄 Found {len(delivered_channels)} channels to process. Starting cleanup...", ephemeral=True)

        # Process channels sequentially to avoid rate limits
        processed_count = 0
        failed_count = 0
        failed_channels = []

        # Check if TOKEN_2 is available
        if not TOKEN_2:
            logger.error("TOKEN_2 not found in environment variables")
            await interaction.followup.send("<:cancel:1360154555295207596> TOKEN_2 not found in environment variables", ephemeral=True)
            return

        # Process each channel one at a time
        for channel in delivered_channels:
            try:
                # Construct the URL for sending messages to the channel
                url = f"https://discord.com/api/v9/channels/{channel.id}/messages"
                headers = {"Authorization": TOKEN_2, "Content-Type": "application/json"}

                # Update status message
                await interaction.followup.send(f"🔄 Processing channel: {channel.name} ({processed_count+1}/{len(delivered_channels)})", ephemeral=True)

                # Send commands with significant delays between them
                async with aiohttp.ClientSession() as session:
                    # Send $close command
                    logger.info(f"Sending $close command to channel {channel.name} ({channel.id})")
                    success = False
                    max_retries = 3
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$close"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $close to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $close. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $close: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $close: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $close to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $transcript command
                    logger.info(f"Sending $transcript command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$transcript"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $transcript to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $transcript. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $transcript: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $transcript: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $transcript to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $delete command
                    logger.info(f"Sending $delete command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$delete"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $delete to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $delete. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $delete: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $delete: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $delete to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                # Successfully processed this channel
                processed_count += 1
                await interaction.followup.send(f"<:check:1360153501866393692> Successfully processed channel: {channel.name}", ephemeral=True)

                # Wait between channels to avoid rate limits
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Error processing channel {channel.name}: {str(e)}")
                logger.error(traceback.format_exc())
                failed_count += 1
                failed_channels.append(channel.name)
                await interaction.followup.send(f"<:cancel:1360154555295207596> Error processing channel {channel.name}: {str(e)}", ephemeral=True)
                await asyncio.sleep(5)  # Wait before continuing to next channel

        # Send completion message
        if failed_count > 0:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Processed {processed_count}/{len(delivered_channels)} channels successfully. "
                f"Failed to process {failed_count} channels: {', '.join(failed_channels)}",
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Successfully processed all {processed_count} channels with 'delivered' in the name.",
                ephemeral=True
            )

        # Track metrics
        execution_time = time.time() - start_time
        if "cleardelivered" not in command_metrics:
            command_metrics["cleardelivered"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["cleardelivered"]['count'] += 1
        command_metrics["cleardelivered"]['total_time'] += execution_time
        command_metrics["cleardelivered"]['max_time'] = max(
            command_metrics["cleardelivered"]['max_time'],
            execution_time
        )

        logger.debug(f"Command cleardelivered executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in cleardelivered command: {e}")
        logger.error(traceback.format_exc())
        if interaction.response.is_done():
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="addtoqueue",
    description="Add the current channel to the queue",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def addtoqueue_command(interaction: discord.Interaction):
    """Slash command to add the current channel to the queue."""
    await track_command_metrics("addtoqueue")(addtoqueue)(interaction)

async def addtoqueue(interaction: discord.Interaction):
    """Add the current channel to the queue."""
    # Track command execution time
    start_time = time.time()

    try:
        # Check if user is authorized
        authorized_users = [572991971359195138, 542140310524788736, 965501524304343082]
        staff_role = discord.utils.get(interaction.guild.roles, id=1340213865748762634)
        if staff_role not in interaction.user.roles and interaction.user.id not in authorized_users:
            await interaction.response.send_message("⛔ You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/addtoqueue` command triggered by {interaction.user} in channel {interaction.channel.name}")

        # Move the channel to the queue
        success = await move_to_queue(interaction.channel)

        if success:
            await interaction.followup.send("<:check:1360153501866393692> Channel added to queue successfully!", ephemeral=True)
        else:
            await interaction.followup.send("<:cancel:1360154555295207596> Failed to add channel to queue.", ephemeral=True)

        # Track metrics
        execution_time = time.time() - start_time
        if "addtoqueue" not in command_metrics:
            command_metrics["addtoqueue"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["addtoqueue"]['count'] += 1
        command_metrics["addtoqueue"]['total_time'] += execution_time
        command_metrics["addtoqueue"]['max_time'] = max(
            command_metrics["addtoqueue"]['max_time'],
            execution_time
        )

        logger.debug(f"Command addtoqueue executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in addtoqueue command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

@bot.tree.command(
    name="cleardelivering",
    description="Close all channels with 'delivering' in the name in the specified category",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def cleardelivering_command(interaction: discord.Interaction):
    """Close all channels with 'delivering' in the name in the specified category."""
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/cleardelivering` command triggered by {interaction.user}")

        # Get the guild and category
        guild = interaction.guild
        category_id = 1354242418211422419
        category = guild.get_channel(category_id)

        if not category:
            await interaction.followup.send(f"<:cancel:1360154555295207596> Category with ID {category_id} not found.", ephemeral=True)
            return

        # Find all channels in the category with "delivering" in the name
        delivering_channels = [channel for channel in category.channels
                             if isinstance(channel, discord.TextChannel) and "delivering" in channel.name.lower()]

        if not delivering_channels:
            await interaction.followup.send("<:check:1360153501866393692> No channels with 'delivering' in the name found in the category.", ephemeral=True)
            return

        # Send initial status message
        await interaction.followup.send(f"🔄 Found {len(delivering_channels)} channels to process. Starting cleanup...", ephemeral=True)

        # Process channels sequentially to avoid rate limits
        processed_count = 0
        failed_count = 0
        failed_channels = []

        # Check if TOKEN_2 is available
        if not TOKEN_2:
            logger.error("TOKEN_2 not found in environment variables")
            await interaction.followup.send("<:cancel:1360154555295207596> TOKEN_2 not found in environment variables", ephemeral=True)
            return

        # Process each channel one at a time
        for channel in delivering_channels:
            try:
                # Construct the URL for sending messages to the channel
                url = f"https://discord.com/api/v9/channels/{channel.id}/messages"
                headers = {"Authorization": TOKEN_2, "Content-Type": "application/json"}

                # Update status message
                await interaction.followup.send(f"🔄 Processing channel: {channel.name} ({processed_count+1}/{len(delivering_channels)})", ephemeral=True)

                # Send commands with significant delays between them
                async with aiohttp.ClientSession() as session:
                    # Send $close command
                    logger.info(f"Sending $close command to channel {channel.name} ({channel.id})")
                    success = False
                    max_retries = 3
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$close"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $close to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $close. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $close: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $close: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $close to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $transcript command
                    logger.info(f"Sending $transcript command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$transcript"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $transcript to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $transcript. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $transcript: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $transcript: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $transcript to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $delete command
                    logger.info(f"Sending $delete command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$delete"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $delete to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $delete. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $delete: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $delete: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $delete to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                # Successfully processed this channel
                processed_count += 1
                await interaction.followup.send(f"<:check:1360153501866393692> Successfully processed channel: {channel.name}", ephemeral=True)

                # Wait between channels to avoid rate limits
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Error processing channel {channel.name}: {str(e)}")
                logger.error(traceback.format_exc())
                failed_count += 1
                failed_channels.append(channel.name)
                await interaction.followup.send(f"<:cancel:1360154555295207596> Error processing channel {channel.name}: {str(e)}", ephemeral=True)
                await asyncio.sleep(5)  # Wait before continuing to next channel

        # Send completion message
        if failed_count > 0:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Processed {processed_count}/{len(delivering_channels)} channels successfully. "
                f"Failed to process {failed_count} channels: {', '.join(failed_channels)}",
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Successfully processed all {processed_count} channels with 'delivering' in the name.",
                ephemeral=True
            )

        # Track metrics
        execution_time = time.time() - start_time
        if "cleardelivering" not in command_metrics:
            command_metrics["cleardelivering"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["cleardelivering"]['count'] += 1
        command_metrics["cleardelivering"]['total_time'] += execution_time
        command_metrics["cleardelivering"]['max_time'] = max(
            command_metrics["cleardelivering"]['max_time'],
            execution_time
        )

        logger.debug(f"Command cleardelivering executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in cleardelivering command: {e}")
        logger.error(traceback.format_exc())
        if interaction.response.is_done():
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

        # Process channels sequentially to avoid rate limits
        processed_count = 0
        failed_count = 0
        failed_channels = []

        # Check if TOKEN_2 is available
        if not TOKEN_2:
            logger.error("TOKEN_2 not found in environment variables")
            await interaction.followup.send("<:cancel:1360154555295207596> TOKEN_2 not found in environment variables", ephemeral=True)
            return

        # Process each channel one at a time
        for channel in delivering_channels:
            try:
                # Construct the URL for sending messages to the channel
                url = f"https://discord.com/api/v9/channels/{channel.id}/messages"
                headers = {"Authorization": TOKEN_2, "Content-Type": "application/json"}

                # Update status message
                await interaction.followup.send(f"🔄 Processing channel: {channel.name} ({processed_count+1}/{len(delivering_channels)})", ephemeral=True)

                # Send commands with significant delays between them
                async with aiohttp.ClientSession() as session:
                    # Send $close command
                    logger.info(f"Sending $close command to channel {channel.name} ({channel.id})")
                    success = False
                    max_retries = 3
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$close"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $close to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $close. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $close: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $close: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $close to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $transcript command
                    logger.info(f"Sending $transcript command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$transcript"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $transcript to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $transcript. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $transcript: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $transcript: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $transcript to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                    # Wait between commands - longer delay to avoid rate limits
                    await asyncio.sleep(3)

                    # Send $delete command
                    logger.info(f"Sending $delete command to channel {channel.name} ({channel.id})")
                    success = False
                    retry_count = 0

                    while not success and retry_count < max_retries:
                        try:
                            async with session.post(url, json={"content": "$delete"}, headers=headers) as response:
                                if response.status == 200:
                                    success = True
                                    logger.info(f"Successfully sent $delete to {channel.name}")
                                elif response.status == 429:  # Rate limited
                                    response_data = await response.json()
                                    retry_after = response_data.get('retry_after', 5)
                                    logger.warning(f"Rate limited on $delete. Waiting {retry_after} seconds")
                                    await interaction.followup.send(f"⏳ Rate limited. Waiting {retry_after} seconds before retrying...", ephemeral=True)
                                    await asyncio.sleep(retry_after + 1)  # Add 1 second buffer
                                else:
                                    logger.error(f"Failed to send $delete: HTTP {response.status}")
                                    await asyncio.sleep(3)  # Wait before retry
                        except Exception as e:
                            logger.error(f"Error sending $delete: {str(e)}")
                            await asyncio.sleep(3)  # Wait before retry

                        retry_count += 1

                    if not success:
                        logger.error(f"Failed to send $delete to {channel.name} after {max_retries} attempts")
                        failed_count += 1
                        failed_channels.append(channel.name)
                        continue

                # Successfully processed this channel
                processed_count += 1
                await interaction.followup.send(f"<:check:1360153501866393692> Successfully processed channel: {channel.name}", ephemeral=True)

                # Wait between channels to avoid rate limits
                await asyncio.sleep(5)

            except Exception as e:
                logger.error(f"Error processing channel {channel.name}: {str(e)}")
                logger.error(traceback.format_exc())
                failed_count += 1
                failed_channels.append(channel.name)
                await interaction.followup.send(f"<:cancel:1360154555295207596> Error processing channel {channel.name}: {str(e)}", ephemeral=True)
                await asyncio.sleep(5)  # Wait before continuing to next channel

        # Send completion message
        if failed_count > 0:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Processed {processed_count}/{len(delivering_channels)} channels successfully. "
                f"Failed to process {failed_count} channels: {', '.join(failed_channels)}",
                ephemeral=True
            )
        else:
            await interaction.followup.send(
                f"<:check:1360153501866393692> Successfully processed all {processed_count} channels with 'delivered' in the name.",
                ephemeral=True
            )

        # Track metrics
        execution_time = time.time() - start_time
        if "cleardelivered" not in command_metrics:
            command_metrics["cleardelivered"] = {'count': 0, 'total_time': 0, 'max_time': 0}

        command_metrics["cleardelivered"]['count'] += 1
        command_metrics["cleardelivered"]['total_time'] += execution_time
        command_metrics["cleardelivered"]['max_time'] = max(
            command_metrics["cleardelivered"]['max_time'],
            execution_time
        )

        logger.debug(f"Command cleardelivered executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in cleardelivered command: {e}")
        logger.error(traceback.format_exc())
        if interaction.response.is_done():
            await interaction.followup.send(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)
        else:
            await interaction.response.send_message(f"<:cancel:1360154555295207596> An error occurred: {e}", ephemeral=True)

# Add a metrics command to show performance statistics
@bot.tree.command(
    name="metrics",
    description="Show bot performance metrics",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.checks.has_any_role(1340213865748762634)  # Staff role check
async def metrics_command(interaction: discord.Interaction):
    """Show bot performance metrics."""
    try:
        # Get memory usage
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_usage = memory_info.rss / 1024 / 1024  # MB

        # Get CPU usage
        cpu_percent = process.cpu_percent(interval=0.5)

        # Create embed with modern design
        embed = discord.Embed(
            title="📊 Bot Performance Dashboard",
            description=f"Real-time performance statistics for **{bot.user.name}**",
            color=discord.Color.from_rgb(91, 141, 238)  # Light Blue
        )

        # Calculate uptime in a more readable format
        uptime_seconds = time.time() - bot.launch_time
        hours, remainder = divmod(uptime_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime_str = f"{int(hours)}h {int(minutes)}m {int(seconds)}s"

        # Add system metrics with icons and better formatting
        embed.add_field(
            name="💻 System Resources",
            value=f"**💾 Memory:** `{memory_usage:.2f} MB`\n"
                  f"**🔌 CPU Load:** `{cpu_percent:.1f}%`\n"
                  f"**⏱️ Uptime:** `{uptime_str}`",
            inline=False
        )

        # Add command metrics
        if command_metrics:
            # Sort by count
            sorted_commands = sorted(
                command_metrics.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )

            commands_value = ""
            for i, (cmd_name, metrics) in enumerate(sorted_commands[:10], 1):  # Show top 10 with ranking
                avg_time = metrics['total_time'] / metrics['count']
                # Format with modern code blocks and better spacing
                commands_value += f"**#{i}** `/{cmd_name}` • {metrics['count']} calls\n"
                commands_value += f"┣ Avg: `{avg_time*1000:.1f}ms` • Max: `{metrics['max_time']*1000:.1f}ms`\n\n"

            embed.add_field(
                name="� Command Usage Stats (Top 10)",
                value=commands_value or "*No commands executed yet.*",
                inline=False
            )
        else:
            embed.add_field(
                name="� Command Usage Stats",
                value="*No commands executed yet.*",
                inline=False
            )

        # Add spacing between sections
        # No divider needed

        # Add background tasks info with better formatting
        tasks_info = ""

        # Background tasks
        if hasattr(bot, 'bg_tasks'):
            running_tasks = sum(1 for t in bot.bg_tasks if not t.done())
            tasks_info += f"**🔄 Background Processes:** `{running_tasks}/{len(bot.bg_tasks)} active`\n"

            # Only show details if there are tasks
            if len(bot.bg_tasks) > 0:
                tasks_info += "\n"
                for i, task in enumerate(bot.bg_tasks):
                    status = "🟢 Running" if not task.done() else "🔴 Completed"
                    tasks_info += f"`Process #{i+1}:` {status}\n"

        # Order tracking tasks
        if hasattr(bot, 'tracking_tasks'):
            active_tracking = sum(1 for t in bot.tracking_tasks if not t.done())
            tasks_info += f"\n**📰 Order Tracking:** `{active_tracking}/{len(bot.tracking_tasks)} active`\n"

        embed.add_field(
            name="<:clock:1360156495517323264> Active Processes",
            value=tasks_info or "*No background tasks running.*",
            inline=False
        )

        # Add footer with timestamp
        embed.set_footer(text=f"Generated at {datetime.datetime.now().strftime('%H:%M:%S')} | The Method")

        await interaction.response.send_message(embed=embed, ephemeral=True)
    except Exception as e:
        logger.error(f"Error in metrics command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(f"<:cancel:1360154555295207596> Error: {e}", ephemeral=True)

# Command error handler
@bot.event
async def on_command_error(ctx, error):
    """Handle command errors."""
    if isinstance(error, commands.CommandOnCooldown):
        await ctx.send(f"⏳ Command on cooldown. Try again in {error.retry_after:.1f} seconds.")
    elif isinstance(error, commands.MissingPermissions):
        await ctx.send("⛔ You don't have permission to use this command.")
    else:
        logger.error(f"Command error: {error}")
        logger.error(traceback.format_exc())
        await ctx.send(f"<:cancel:1360154555295207596> An error occurred: {error}")

# Command error handler - simplified version without command queue

# Application command error handler
@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle application command errors."""
    if isinstance(error, app_commands.CommandOnCooldown):
        # Simply inform the user about the cooldown without queueing
        command_name = interaction.command.name if interaction.command else "unknown"
        await interaction.response.send_message(
            f"⏳ Command `/{command_name}` is on cooldown. Please try again in {error.retry_after:.1f} seconds.",
            ephemeral=True
        )
    elif isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message(
            "⛔ You don't have permission to use this command.",
            ephemeral=True
        )
    else:
        logger.error(f"App command error: {error}")
        logger.error(traceback.format_exc())

        # Try to respond if not already responded
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"<:cancel:1360154555295207596> An error occurred: {error}",
                    ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    f"<:cancel:1360154555295207596> An error occurred: {error}",
                    ephemeral=True
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

# Command metrics decorator
def track_command_metrics(command_name):
    """Decorator to track command execution metrics."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                return await func(*args, **kwargs)
            finally:
                execution_time = time.time() - start_time
                if command_name not in command_metrics:
                    command_metrics[command_name] = {
                        'count': 0,
                        'total_time': 0,
                        'max_time': 0
                    }

                command_metrics[command_name]['count'] += 1
                command_metrics[command_name]['total_time'] += execution_time
                command_metrics[command_name]['max_time'] = max(
                    command_metrics[command_name]['max_time'],
                    execution_time
                )

                logger.debug(
                    f"Command {command_name} executed in {execution_time:.4f}s "
                    f"(avg: {command_metrics[command_name]['total_time'] / command_metrics[command_name]['count']:.4f}s)"
                )
        return wrapper
    return decorator

# Graceful shutdown
async def cleanup():
    """Clean up resources before shutdown."""
    logger.info("Cleaning up resources...")

    # Save tracking data before shutdown
    await save_tracking_data()
    logger.info("Saved tracking data for resuming on next startup")

    # Save clock data before shutdown
    await save_clock_data()
    logger.info("Saved clock data for resuming on next startup")

    # Cancel background tasks
    if hasattr(bot, 'bg_tasks'):
        for task in bot.bg_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Cancel tracking tasks
    if hasattr(bot, 'tracking_tasks'):
        for task in bot.tracking_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Close HTTP session
    global _http_session
    if _http_session and not _http_session.closed:
        await _http_session.close()
        logger.info("HTTP session closed")

def run_bot():
    try:
        # Run the bot with proper signal handling
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("Bot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_bot()